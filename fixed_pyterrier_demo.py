#!/usr/bin/env python3
"""
Fixed PyTerrier Demo

A working PyTerrier demo that handles initialization issues properly
"""

import os
import json
import pandas as pd
import time
from typing import List, Dict


def safe_pyterrier_init():
    """Safely initialize PyTerrier with proper error handling"""
    try:
        import pyterrier as pt
        
        # Check if already started
        if hasattr(pt, 'java') and pt.java.started():
            print("✅ PyTerrier already initialized")
            return True, pt
        
        print("🔄 Initializing PyTerrier...")
        
        # Try different initialization approaches
        try:
            # Method 1: Simple init
            pt.init()
            print("✅ PyTerrier initialized successfully")
            return True, pt
            
        except Exception as e1:
            print(f"⚠️  Standard init failed: {e1}")
            
            try:
                # Method 2: Force download of dependencies
                print("🔄 Trying to force download dependencies...")
                pt.init(boot_packages=["com.github.terrierteam:terrier-core:5.8"])
                print("✅ PyTerrier initialized with forced dependencies")
                return True, pt
                
            except Exception as e2:
                print(f"⚠️  Forced init failed: {e2}")
                
                try:
                    # Method 3: Use specific versions
                    print("🔄 Trying with specific versions...")
                    pt.init(version="5.8", helper_version="0.0.8")
                    print("✅ PyTerrier initialized with specific versions")
                    return True, pt
                    
                except Exception as e3:
                    print(f"❌ All PyTerrier init methods failed: {e3}")
                    return False, None
                    
    except ImportError:
        print("❌ PyTerrier not installed")
        return False, None


class FixedPyTerrierDemo:
    """Fixed PyTerrier demo with robust error handling"""
    
    def __init__(self):
        self.pt_available = False
        self.pt = None
        self.index = None
        self.retriever = None
        self.documents = []
        
        # Try to initialize PyTerrier
        self.pt_available, self.pt = safe_pyterrier_init()
    
    def create_documents(self):
        """Create sample documents"""
        self.documents = [
            {
                "docno": "doc1",
                "text": "Machine learning is a subset of artificial intelligence that enables computers to learn from data without being explicitly programmed."
            },
            {
                "docno": "doc2",
                "text": "Deep learning uses neural networks with multiple layers to model complex patterns in data for tasks like image recognition and natural language processing."
            },
            {
                "docno": "doc3",
                "text": "Information retrieval is the activity of obtaining information system resources that are relevant to an information need from a collection of those resources."
            },
            {
                "docno": "doc4",
                "text": "BM25 is a ranking function used by search engines to estimate the relevance of documents to a given search query based on term frequency and document length."
            },
            {
                "docno": "doc5",
                "text": "PyTerrier is a Python framework for performing information retrieval experiments with the Terrier search engine."
            }
        ]
        
        print(f"✅ Created {len(self.documents)} sample documents")
        return self.documents
    
    def build_index(self):
        """Build PyTerrier index"""
        if not self.pt_available:
            print("❌ PyTerrier not available for indexing")
            return False
        
        try:
            print("🔨 Building PyTerrier index...")
            
            # Create index directory
            index_path = "./demo_index"
            if os.path.exists(index_path):
                import shutil
                shutil.rmtree(index_path)
            
            # Create indexer
            indexer = self.pt.IterDictIndexer(index_path, overwrite=True)
            
            # Index documents
            index_ref = indexer.index(self.documents)
            self.index = self.pt.IndexFactory.of(index_ref)
            
            # Create retriever
            self.retriever = self.pt.BatchRetrieve(self.index, wmodel="BM25")
            
            print("✅ Index built successfully")
            
            # Show index stats
            stats = self.index.getCollectionStatistics()
            print(f"📊 Index statistics:")
            print(f"   Documents: {stats.getNumberOfDocuments()}")
            print(f"   Terms: {stats.getNumberOfUniqueTerms()}")
            print(f"   Tokens: {stats.getNumberOfTokens()}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error building index: {e}")
            return False
    
    def search(self, query: str, top_k: int = 5):
        """Perform search"""
        if not self.retriever:
            print("❌ No retriever available")
            return []
        
        try:
            # Create query dataframe
            query_df = pd.DataFrame([{"qid": "1", "query": query}])
            
            # Perform search
            results = self.retriever.transform(query_df)
            
            # Limit results
            if len(results) > top_k:
                results = results.head(top_k)
            
            return results
            
        except Exception as e:
            print(f"❌ Search error: {e}")
            return pd.DataFrame()
    
    def display_results(self, results, query):
        """Display search results"""
        print(f"\n🔍 Search Results for: '{query}'")
        print("=" * 60)
        
        if results.empty:
            print("No results found")
            return
        
        for idx, (_, row) in enumerate(results.iterrows(), 1):
            docno = row.get('docno', 'Unknown')
            score = row.get('score', 0)
            
            # Find document text
            doc_text = "Document text not found"
            for doc in self.documents:
                if doc['docno'] == docno:
                    doc_text = doc['text'][:100] + "..." if len(doc['text']) > 100 else doc['text']
                    break
            
            print(f"{idx}. [{docno}] Score: {score:.4f}")
            print(f"   {doc_text}")
            print()
    
    def run_demo_searches(self):
        """Run demo searches"""
        demo_queries = [
            "machine learning artificial intelligence",
            "neural networks deep learning",
            "information retrieval search",
            "BM25 ranking function"
        ]
        
        for query in demo_queries:
            results = self.search(query)
            self.display_results(results, query)
    
    def interactive_search(self):
        """Interactive search interface"""
        print("\n🔍 Interactive Search")
        print("=" * 40)
        print("Enter search queries (type 'quit' to exit)")
        
        while True:
            try:
                query = input("\nSearch> ").strip()
                
                if query.lower() == 'quit':
                    break
                elif query:
                    results = self.search(query)
                    self.display_results(results, query)
                    
            except KeyboardInterrupt:
                break
        
        print("\n👋 Goodbye!")
    
    def run_complete_demo(self):
        """Run complete demo"""
        print("🚀 Fixed PyTerrier Demo")
        print("=" * 40)
        
        # Create documents
        self.create_documents()
        
        if self.pt_available:
            # Build index
            if self.build_index():
                # Run demo searches
                print("\n🎯 Running Demo Searches")
                self.run_demo_searches()
                
                # Interactive search
                choice = input("\nStart interactive search? (y/n): ").strip().lower()
                if choice in ['y', 'yes']:
                    self.interactive_search()
                
                print("\n✅ Demo completed successfully!")
            else:
                print("❌ Failed to build index")
        else:
            print("❌ PyTerrier not available")
            print("\n💡 Alternative options:")
            print("1. Use the alternative search demo: python alternative_search_demo.py")
            print("2. Fix PyTerrier installation and try again")


def main():
    """Main function"""
    demo = FixedPyTerrierDemo()
    demo.run_complete_demo()


if __name__ == "__main__":
    main()
