#!/usr/bin/env python3
"""
测试脚本：验证环境设置和组件是否正常工作
"""

import os
import sys
import json


def test_imports():
    """测试所有必要的导入"""
    print("Testing imports...")
    
    try:
        import numpy as np
        print("✅ numpy")
    except ImportError as e:
        print(f"❌ numpy: {e}")
        return False
    
    try:
        import bm25s
        print("✅ bm25s")
    except ImportError as e:
        print(f"❌ bm25s: {e}")
        return False
    
    try:
        from openai import OpenAI
        print("✅ openai")
    except ImportError as e:
        print(f"❌ openai: {e}")
        return False
    
    try:
        import httpx
        print("✅ httpx")
    except ImportError as e:
        print(f"❌ httpx: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv")
    except ImportError as e:
        print(f"❌ python-dotenv: {e}")
        return False
    
    return True


def test_project_modules():
    """测试项目模块"""
    print("\nTesting project modules...")
    
    try:
        from config import Config
        print("✅ config")
    except ImportError as e:
        print(f"❌ config: {e}")
        return False
    
    try:
        from src.llm4ranking.lm.openrouter import OpenRouterClient
        print("✅ OpenRouterClient")
    except ImportError as e:
        print(f"❌ OpenRouterClient: {e}")
        return False
    
    try:
        from src.llm4ranking.datasets import DL19Dataset
        print("✅ DL19Dataset")
    except ImportError as e:
        print(f"❌ DL19Dataset: {e}")
        return False
    
    try:
        from src.llm4ranking.evaluation.metrics import RankingEvaluator
        print("✅ RankingEvaluator")
    except ImportError as e:
        print(f"❌ RankingEvaluator: {e}")
        return False
    
    try:
        from src.llm4ranking import Reranker
        print("✅ Reranker")
    except ImportError as e:
        print(f"❌ Reranker: {e}")
        return False
    
    return True


def test_data_files():
    """测试数据文件"""
    print("\nTesting data files...")
    
    if not os.path.exists('toy_data/dl19_bm25_top20.jsonl'):
        print("❌ toy_data/dl19_bm25_top20.jsonl not found")
        return False
    
    try:
        with open('toy_data/dl19_bm25_top20.jsonl', 'r', encoding='utf-8') as f:
            first_line = f.readline()
            data = json.loads(first_line)
            
            # 检查数据格式
            if 'query' not in data:
                print("❌ Missing 'query' field in data")
                return False
            
            if 'hits' not in data:
                print("❌ Missing 'hits' field in data")
                return False
            
            if not data['hits']:
                print("❌ Empty 'hits' field in data")
                return False
            
            # 检查 hits 格式
            hit = data['hits'][0]
            required_fields = ['qid', 'docid', 'content', 'score']
            for field in required_fields:
                if field not in hit:
                    print(f"❌ Missing '{field}' field in hit")
                    return False
        
        print("✅ toy_data/dl19_bm25_top20.jsonl")
        
        # 统计数据
        with open('toy_data/dl19_bm25_top20.jsonl', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"   📊 Found {len(lines)} queries in dataset")
        
    except Exception as e:
        print(f"❌ Error reading toy_data/dl19_bm25_top20.jsonl: {e}")
        return False
    
    return True


def test_config():
    """测试配置"""
    print("\nTesting configuration...")
    
    if not os.path.exists('.env'):
        print("⚠️  .env file not found (this is OK for testing)")
        print("   Copy .env.example to .env and fill in your API key to run the full demo")
        return True
    
    try:
        from config import Config
        
        if Config.OPENROUTER_API_KEY:
            print("✅ OPENROUTER_API_KEY found")
        else:
            print("⚠️  OPENROUTER_API_KEY not set")
        
        print(f"✅ Default model: {Config.DEFAULT_MODEL}")
        print(f"✅ Base URL: {Config.OPENROUTER_BASE_URL}")
        
        proxy_config = Config.get_proxy_config()
        if proxy_config:
            print(f"✅ Proxy configured: {proxy_config}")
        else:
            print("ℹ️  No proxy configured")
        
    except Exception as e:
        print(f"❌ Error testing config: {e}")
        return False
    
    return True


def test_basic_functionality():
    """测试基本功能"""
    print("\nTesting basic functionality...")
    
    try:
        # 测试数据加载
        from src.llm4ranking.datasets import DL19Dataset
        dataset = DL19Dataset()
        dataset.load_from_toy_data()
        
        queries = dataset.get_queries()
        documents = dataset.get_documents()
        
        print(f"✅ Loaded {len(queries)} queries and {len(documents)} documents")
        
        # 测试评估器
        from src.llm4ranking.evaluation.metrics import RankingEvaluator
        evaluator = RankingEvaluator()
        
        # 创建虚拟结果进行测试
        dummy_ranked_docs = list(documents.keys())[:10]
        dummy_qrels = {doc_id: 1 if i < 5 else 0 for i, doc_id in enumerate(dummy_ranked_docs)}
        
        metrics = evaluator.evaluate_query(dummy_ranked_docs, dummy_qrels)
        print(f"✅ Evaluation metrics calculated: {list(metrics.keys())}")
        
    except Exception as e:
        print(f"❌ Error testing basic functionality: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("="*60)
    print("DL19 BM25 + LLM Reranking Demo - Setup Test")
    print("="*60)
    
    all_passed = True
    
    # 运行所有测试
    tests = [
        test_imports,
        test_project_modules,
        test_data_files,
        test_config,
        test_basic_functionality
    ]
    
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("✅ All tests passed! You're ready to run the demo.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env (if not done already)")
        print("2. Add your OpenRouter API key to .env")
        print("3. Run: python run_demo.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        print("\nCommon solutions:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check file paths and permissions")
        print("3. Verify data file format")
    print("="*60)
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit(main())
