# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Model Configuration
DEFAULT_MODEL=openai/gpt-4-1106-preview
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Proxy Configuration (optional)
HTTP_PROXY=http://127.0.0.1:1087
HTTPS_PROXY=http://127.0.0.1:1087

# Experiment Configuration
TOP_K_RETRIEVAL=100
TOP_K_RERANK=20
BATCH_SIZE=32

# Demo Configuration
DEMO_QUERIES_LIMIT=5
DEMO_SHOW_PROGRESS=true
DEMO_SAVE_RESULTS=true
