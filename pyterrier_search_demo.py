#!/usr/bin/env python3
"""
PyTerrier Interactive Search Demo

A complete interactive search system using PyTerrier with:
- Document collection management
- Real-time indexing
- Multiple retrieval models
- Interactive search interface
- Result visualization
"""

import os
import json
import pandas as pd
from typing import List, Dict, Optional, Tuple
import time
from datetime import datetime


def check_pyterrier():
    """Check if PyTerrier is available"""
    try:
        import pyterrier as pt
        return True, pt
    except ImportError:
        return False, None


class PyTerrierSearchDemo:
    """Interactive PyTerrier Search Demo"""
    
    def __init__(self, data_dir: str = "search_demo_data"):
        """Initialize the search demo"""
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        self.pt_available, self.pt = check_pyterrier()
        self.index = None
        self.retrievers = {}
        self.documents = []
        
        if self.pt_available and not self.pt.started():
            self.pt.init()
            print("✅ PyTerrier initialized")
    
    def create_sample_collection(self) -> List[Dict]:
        """Create a comprehensive sample document collection"""
        print("📚 Creating sample document collection...")
        
        documents = [
            {
                "docno": "tech001",
                "title": "Introduction to Machine Learning",
                "text": "Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed. It involves algorithms that can identify patterns, make predictions, and improve performance over time.",
                "category": "Technology",
                "author": "Dr. Sarah Chen",
                "date": "2024-01-15"
            },
            {
                "docno": "tech002", 
                "title": "Deep Learning and Neural Networks",
                "text": "Deep learning uses artificial neural networks with multiple layers to model and understand complex patterns in data. These networks can process images, text, and speech with remarkable accuracy, powering applications like computer vision and natural language processing.",
                "category": "Technology",
                "author": "Prof. Michael Rodriguez",
                "date": "2024-02-20"
            },
            {
                "docno": "health001",
                "title": "Benefits of Regular Exercise",
                "text": "Regular physical exercise provides numerous health benefits including improved cardiovascular health, stronger muscles and bones, better mental health, and increased longevity. Even moderate exercise like walking 30 minutes daily can significantly improve overall well-being.",
                "category": "Health",
                "author": "Dr. Emily Johnson",
                "date": "2024-01-10"
            },
            {
                "docno": "health002",
                "title": "Nutrition and Healthy Eating",
                "text": "A balanced diet rich in fruits, vegetables, whole grains, and lean proteins is essential for optimal health. Proper nutrition supports immune function, maintains healthy weight, and reduces the risk of chronic diseases like diabetes and heart disease.",
                "category": "Health", 
                "author": "Nutritionist Lisa Wang",
                "date": "2024-02-05"
            },
            {
                "docno": "science001",
                "title": "Climate Change and Global Warming",
                "text": "Climate change refers to long-term shifts in global temperatures and weather patterns. Human activities, particularly burning fossil fuels, have accelerated global warming, leading to rising sea levels, extreme weather events, and ecosystem disruption.",
                "category": "Science",
                "author": "Dr. James Thompson",
                "date": "2024-01-25"
            },
            {
                "docno": "science002",
                "title": "Renewable Energy Technologies",
                "text": "Renewable energy sources like solar, wind, and hydroelectric power offer sustainable alternatives to fossil fuels. These technologies are becoming increasingly efficient and cost-effective, playing a crucial role in reducing carbon emissions and combating climate change.",
                "category": "Science",
                "author": "Dr. Maria Gonzalez",
                "date": "2024-02-12"
            },
            {
                "docno": "travel001",
                "title": "Exploring European Cities",
                "text": "Europe offers incredible diversity in culture, architecture, and cuisine. From the romantic canals of Venice to the historic streets of Prague, each city provides unique experiences. Popular destinations include Paris, Barcelona, Amsterdam, and Vienna.",
                "category": "Travel",
                "author": "Travel Writer Alex Kim",
                "date": "2024-01-30"
            },
            {
                "docno": "travel002",
                "title": "Adventure Travel in South America",
                "text": "South America is a paradise for adventure travelers, offering activities like hiking Machu Picchu in Peru, exploring the Amazon rainforest, trekking in Patagonia, and experiencing the vibrant culture of cities like Rio de Janeiro and Buenos Aires.",
                "category": "Travel",
                "author": "Adventure Guide Carlos Silva",
                "date": "2024-02-18"
            },
            {
                "docno": "food001",
                "title": "Mediterranean Cuisine and Health",
                "text": "Mediterranean cuisine, rich in olive oil, fish, vegetables, and whole grains, is associated with numerous health benefits. This diet pattern has been linked to reduced risk of heart disease, improved brain function, and increased longevity.",
                "category": "Food",
                "author": "Chef Isabella Romano",
                "date": "2024-01-20"
            },
            {
                "docno": "food002",
                "title": "Asian Cooking Techniques",
                "text": "Asian cuisine encompasses diverse cooking methods including stir-frying, steaming, braising, and fermentation. These techniques preserve nutrients while creating complex flavors. Popular dishes include sushi, pad thai, dim sum, and curry.",
                "category": "Food",
                "author": "Chef Hiroshi Tanaka",
                "date": "2024-02-08"
            }
        ]
        
        # Save documents
        docs_path = os.path.join(self.data_dir, "documents.jsonl")
        with open(docs_path, 'w', encoding='utf-8') as f:
            for doc in documents:
                f.write(json.dumps(doc, ensure_ascii=False) + '\n')
        
        self.documents = documents
        print(f"✅ Created {len(documents)} documents across {len(set(d['category'] for d in documents))} categories")
        
        return documents
    
    def build_index(self) -> bool:
        """Build PyTerrier index"""
        if not self.pt_available:
            print("❌ PyTerrier not available")
            return False
        
        index_path = os.path.join(self.data_dir, "search_index")
        
        if os.path.exists(index_path) and os.listdir(index_path):
            print(f"✅ Loading existing index: {index_path}")
            self.index = self.pt.IndexFactory.of(index_path)
            return True
        
        print("🔨 Building search index...")
        
        try:
            # Document iterator for indexing
            def doc_iterator():
                for doc in self.documents:
                    # Combine title and text for better searchability
                    full_text = f"{doc['title']} {doc['text']}"
                    yield {
                        'docno': doc['docno'],
                        'text': full_text,
                        'title': doc['title'],
                        'category': doc['category'],
                        'author': doc['author'],
                        'date': doc['date']
                    }
            
            # Build index with metadata
            indexer = self.pt.IterDictIndexer(
                index_path,
                verbose=True,
                meta=['title', 'category', 'author', 'date']
            )
            
            index_ref = indexer.index(doc_iterator())
            self.index = self.pt.IndexFactory.of(index_ref)
            
            # Display index statistics
            stats = self.index.getCollectionStatistics()
            print(f"✅ Index built successfully!")
            print(f"📊 Index Statistics:")
            print(f"   Documents: {stats.getNumberOfDocuments()}")
            print(f"   Unique Terms: {stats.getNumberOfUniqueTerms()}")
            print(f"   Total Tokens: {stats.getNumberOfTokens()}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error building index: {e}")
            return False
    
    def setup_retrievers(self) -> bool:
        """Setup multiple retrieval models"""
        if not self.index:
            print("❌ Index not available")
            return False
        
        print("🔧 Setting up retrieval models...")
        
        try:
            # BM25 (default)
            self.retrievers['BM25'] = self.pt.BatchRetrieve(
                self.index, 
                wmodel="BM25",
                metadata=['title', 'category', 'author', 'date']
            )
            
            # TF-IDF
            self.retrievers['TF_IDF'] = self.pt.BatchRetrieve(
                self.index,
                wmodel="TF_IDF", 
                metadata=['title', 'category', 'author', 'date']
            )
            
            # DPH (Divergence from Randomness)
            self.retrievers['DPH'] = self.pt.BatchRetrieve(
                self.index,
                wmodel="DPH",
                metadata=['title', 'category', 'author', 'date']
            )
            
            print(f"✅ Setup {len(self.retrievers)} retrieval models: {list(self.retrievers.keys())}")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up retrievers: {e}")
            return False
    
    def search(self, query: str, model: str = "BM25", num_results: int = 5) -> pd.DataFrame:
        """Perform search with specified model"""
        if not self.pt_available:
            return self._fallback_search(query, num_results)
        
        if model not in self.retrievers:
            print(f"❌ Model '{model}' not available. Using BM25.")
            model = "BM25"
        
        try:
            # Create query dataframe
            query_df = pd.DataFrame([{'qid': '1', 'query': query}])
            
            # Perform search
            start_time = time.time()
            results = self.retrievers[model].transform(query_df)
            search_time = time.time() - start_time
            
            # Limit results
            if len(results) > num_results:
                results = results.head(num_results)
            
            # Add search metadata
            results['search_time'] = search_time
            results['model'] = model
            
            return results
            
        except Exception as e:
            print(f"❌ Search error: {e}")
            return pd.DataFrame()
    
    def _fallback_search(self, query: str, num_results: int = 5) -> pd.DataFrame:
        """Fallback search when PyTerrier is not available"""
        print("🔍 Using fallback keyword search...")
        
        query_terms = query.lower().split()
        results = []
        
        for doc in self.documents:
            # Simple keyword matching
            text = f"{doc['title']} {doc['text']}".lower()
            score = sum(1 for term in query_terms if term in text)
            
            if score > 0:
                results.append({
                    'qid': '1',
                    'docno': doc['docno'],
                    'score': score,
                    'title': doc['title'],
                    'category': doc['category'],
                    'author': doc['author'],
                    'date': doc['date'],
                    'model': 'Keyword_Match'
                })
        
        # Sort by score and limit results
        results.sort(key=lambda x: x['score'], reverse=True)
        results = results[:num_results]
        
        return pd.DataFrame(results)
    
    def display_results(self, results: pd.DataFrame, query: str):
        """Display search results in a formatted way"""
        if results.empty:
            print("❌ No results found")
            return
        
        print(f"\n🔍 Search Results for: '{query}'")
        print("=" * 80)
        
        # Display search metadata
        if 'search_time' in results.columns:
            search_time = results['search_time'].iloc[0]
            model = results['model'].iloc[0]
            print(f"📊 Found {len(results)} results in {search_time:.3f}s using {model}")
        else:
            model = results['model'].iloc[0] if 'model' in results.columns else 'Unknown'
            print(f"📊 Found {len(results)} results using {model}")
        
        print("-" * 80)
        
        # Display each result
        for idx, (_, row) in enumerate(results.iterrows(), 1):
            score = row.get('score', 0)
            title = row.get('title', 'No Title')
            category = row.get('category', 'Unknown')
            author = row.get('author', 'Unknown')
            date = row.get('date', 'Unknown')
            docno = row.get('docno', 'Unknown')
            
            print(f"{idx}. [{docno}] {title}")
            print(f"   📂 Category: {category} | ✍️  Author: {author} | 📅 Date: {date}")
            print(f"   ⭐ Score: {score:.4f}")
            
            # Show document snippet if available
            doc_data = next((d for d in self.documents if d['docno'] == docno), None)
            if doc_data:
                snippet = doc_data['text'][:150] + "..." if len(doc_data['text']) > 150 else doc_data['text']
                print(f"   📝 {snippet}")
            
            print()
    
    def interactive_search(self):
        """Run interactive search interface"""
        print("\n🚀 PyTerrier Interactive Search Demo")
        print("=" * 60)
        print("Available commands:")
        print("  - Type your search query")
        print("  - 'models' - show available models")
        print("  - 'stats' - show collection statistics") 
        print("  - 'help' - show this help")
        print("  - 'quit' - exit")
        print("=" * 60)
        
        current_model = "BM25"
        
        while True:
            try:
                # Get user input
                user_input = input(f"\n🔍 Search ({current_model})> ").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.lower() == 'quit':
                    print("👋 Goodbye!")
                    break
                
                elif user_input.lower() == 'help':
                    print("\n📚 Help:")
                    print("  - Enter any search query (e.g., 'machine learning', 'healthy food')")
                    print("  - Use 'model:MODEL_NAME query' to change model (e.g., 'model:TF_IDF neural networks')")
                    print("  - Available models:", list(self.retrievers.keys()) if self.retrievers else ['Keyword_Match'])
                    continue
                
                elif user_input.lower() == 'models':
                    if self.retrievers:
                        print(f"\n🔧 Available models: {list(self.retrievers.keys())}")
                        print(f"   Current model: {current_model}")
                    else:
                        print("\n🔧 Available models: ['Keyword_Match'] (PyTerrier not available)")
                    continue
                
                elif user_input.lower() == 'stats':
                    print(f"\n📊 Collection Statistics:")
                    print(f"   Total documents: {len(self.documents)}")
                    categories = set(d['category'] for d in self.documents)
                    print(f"   Categories: {', '.join(sorted(categories))}")
                    if self.index:
                        stats = self.index.getCollectionStatistics()
                        print(f"   Indexed terms: {stats.getNumberOfUniqueTerms()}")
                        print(f"   Total tokens: {stats.getNumberOfTokens()}")
                    continue
                
                # Handle model change
                if user_input.startswith('model:'):
                    parts = user_input.split(' ', 1)
                    if len(parts) == 2:
                        model_part, query = parts
                        new_model = model_part.split(':', 1)[1]
                        if new_model in self.retrievers:
                            current_model = new_model
                            user_input = query
                        else:
                            print(f"❌ Unknown model: {new_model}")
                            print(f"   Available: {list(self.retrievers.keys())}")
                            continue
                    else:
                        print("❌ Usage: model:MODEL_NAME query")
                        continue
                
                # Perform search
                print(f"🔍 Searching for: '{user_input}'...")
                results = self.search(user_input, current_model, num_results=5)
                self.display_results(results, user_input)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def run_demo_searches(self):
        """Run predefined demo searches"""
        print("\n🎯 Running Demo Searches")
        print("=" * 50)
        
        demo_queries = [
            "machine learning artificial intelligence",
            "healthy food nutrition diet",
            "climate change renewable energy",
            "travel europe cities",
            "exercise health benefits"
        ]
        
        for i, query in enumerate(demo_queries, 1):
            print(f"\n📝 Demo Search {i}/{len(demo_queries)}")
            
            # Try different models if available
            models_to_try = ['BM25', 'TF_IDF'] if self.retrievers else ['Keyword_Match']
            
            for model in models_to_try:
                if model in self.retrievers or model == 'Keyword_Match':
                    results = self.search(query, model, num_results=3)
                    self.display_results(results, f"{query} ({model})")
                    
                    if len(models_to_try) > 1:
                        input("Press Enter to continue...")
    
    def run_complete_demo(self):
        """Run the complete search demo"""
        print("🚀 PyTerrier Search Demo")
        print("=" * 40)
        
        # 1. Create document collection
        self.create_sample_collection()
        
        # 2. Build index (if PyTerrier available)
        if self.pt_available:
            if not self.build_index():
                print("❌ Failed to build index")
                return
            
            # 3. Setup retrievers
            if not self.setup_retrievers():
                print("❌ Failed to setup retrievers")
                return
        else:
            print("⚠️  PyTerrier not available - using fallback search")
        
        # 4. Run demo searches
        self.run_demo_searches()
        
        # 5. Interactive search
        print(f"\n🎮 Ready for interactive search!")
        choice = input("Start interactive search? (y/n): ").strip().lower()
        
        if choice in ['y', 'yes']:
            self.interactive_search()
        
        print("\n✅ Demo completed!")


def main():
    """Main function"""
    print("🔍 PyTerrier Interactive Search Demo")
    print("=" * 50)
    
    # Check PyTerrier availability
    pt_available, _ = check_pyterrier()
    
    if not pt_available:
        print("⚠️  PyTerrier not installed. Features available:")
        print("   ✅ Document collection creation")
        print("   ✅ Fallback keyword search")
        print("   ❌ Advanced retrieval models")
        print("   ❌ Index-based search")
        print("\n💡 To install PyTerrier:")
        print("   pip install python-terrier")
        print("   (Requires Java 8+ and Python 3.8-3.11)")
    
    # Run demo
    demo = PyTerrierSearchDemo()
    demo.run_complete_demo()


if __name__ == "__main__":
    main()
