import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import os

def analyze_results(results_df, qrels_df, output_dir="./analysis"):
    """Analyze and visualize retrieval results"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Score distribution
    plt.figure(figsize=(10, 6))
    plt.hist(results_df['score'], bins=50, alpha=0.7, color='blue', edgecolor='black')
    plt.xlabel('BM25 Score')
    plt.ylabel('Frequency')
    plt.title('Distribution of BM25 Retrieval Scores')
    plt.savefig(os.path.join(output_dir, 'score_distribution.png'))
    plt.close()
    
    # 2. Per-query performance
    per_query_scores = results_df.groupby('qid')['score'].describe()
    
    plt.figure(figsize=(12, 6))
    plt.bar(range(len(per_query_scores)), per_query_scores['mean'])
    plt.xlabel('Query Index')
    plt.ylabel('Mean BM25 Score')
    plt.title('Mean BM25 Score per Query')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'per_query_performance.png'))
    plt.close()
    
    # 3. Rank position analysis
    rank_positions = results_df.groupby('qid')['rank'].value_counts().unstack(fill_value=0)
    
    plt.figure(figsize=(10, 6))
    avg_rank_dist = rank_positions.mean()[:50]  # Top 50 ranks
    plt.plot(avg_rank_dist.index, avg_rank_dist.values, marker='o', markersize=4)
    plt.xlabel('Rank Position')
    plt.ylabel('Average Number of Documents')
    plt.title('Average Document Distribution by Rank Position')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(output_dir, 'rank_distribution.png'))
    plt.close()
    
    print(f"Analysis plots saved to {output_dir}")
    
    return per_query_scores

# Usage example
if __name__ == "__main__":
    # Load results
    results = pd.read_csv("./results/dl19_bm25_results.csv")
    
    # Run analysis
    analyze_results(results, None)
