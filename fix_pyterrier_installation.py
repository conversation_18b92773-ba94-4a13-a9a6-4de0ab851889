#!/usr/bin/env python3
"""
PyTerrier Installation Fix

This script provides multiple solutions for PyTerrier installation issues,
particularly the pyjnius compilation error on newer Python versions.
"""

import sys
import subprocess
import platform
import os
from typing import List, Tuple


class PyTerrierFixer:
    """PyTerrier Installation Fixer"""
    
    def __init__(self):
        self.python_version = sys.version_info
        self.system = platform.system()
        self.architecture = platform.machine()
    
    def diagnose_issue(self) -> List[str]:
        """Diagnose the installation issue"""
        issues = []
        
        print("🔍 Diagnosing PyTerrier installation issues...")
        print(f"Python version: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"System: {self.system} ({self.architecture})")
        
        # Check Python version compatibility
        if self.python_version >= (3, 12):
            issues.append("Python 3.12+ compatibility issue with pyjnius")
        
        # Check Java availability
        java_available = self._check_java()
        if not java_available:
            issues.append("Java not found or not properly configured")
        
        # Check Cython
        cython_available = self._check_cython()
        if not cython_available:
            issues.append("Cython not available for compilation")
        
        return issues
    
    def _check_java(self) -> bool:
        """Check if Java is available"""
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ Java is available")
                return True
            else:
                print("❌ Java not working properly")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Java not found")
            return False
    
    def _check_cython(self) -> bool:
        """Check if Cython is available"""
        try:
            import Cython
            print(f"✅ Cython available: {Cython.__version__}")
            return True
        except ImportError:
            print("❌ Cython not available")
            return False
    
    def solution_1_compatible_python(self):
        """Solution 1: Use compatible Python version"""
        print("\n🔧 Solution 1: Use Compatible Python Version")
        print("=" * 50)
        
        print("PyTerrier works best with Python 3.8-3.11")
        print("\nRecommended steps:")
        
        if self.system == "Darwin":  # macOS
            print("📱 macOS:")
            print("   # Using pyenv")
            print("   brew install pyenv")
            print("   pyenv install 3.11.7")
            print("   pyenv virtualenv 3.11.7 pyterrier-env")
            print("   pyenv activate pyterrier-env")
            print("   pip install python-terrier")
            print()
            print("   # Using conda")
            print("   conda create -n pyterrier python=3.11")
            print("   conda activate pyterrier")
            print("   pip install python-terrier")
        
        elif self.system == "Linux":
            print("🐧 Linux:")
            print("   # Using conda")
            print("   conda create -n pyterrier python=3.11")
            print("   conda activate pyterrier")
            print("   pip install python-terrier")
            print()
            print("   # Using pyenv")
            print("   curl https://pyenv.run | bash")
            print("   pyenv install 3.11.7")
            print("   pyenv virtualenv 3.11.7 pyterrier-env")
            print("   pyenv activate pyterrier-env")
            print("   pip install python-terrier")
        
        elif self.system == "Windows":
            print("🪟 Windows:")
            print("   # Using conda")
            print("   conda create -n pyterrier python=3.11")
            print("   conda activate pyterrier")
            print("   pip install python-terrier")
    
    def solution_2_precompiled_wheels(self):
        """Solution 2: Use precompiled wheels"""
        print("\n🔧 Solution 2: Use Precompiled Wheels")
        print("=" * 50)
        
        print("Try installing precompiled wheels:")
        print()
        print("# Update pip and setuptools")
        print("pip install --upgrade pip setuptools wheel")
        print()
        print("# Install Cython first")
        print("pip install Cython")
        print()
        print("# Try installing pyjnius with specific options")
        print("pip install pyjnius --no-cache-dir --force-reinstall")
        print()
        print("# Then install PyTerrier")
        print("pip install python-terrier")
    
    def solution_3_docker(self):
        """Solution 3: Use Docker"""
        print("\n🔧 Solution 3: Use Docker (Recommended)")
        print("=" * 50)
        
        dockerfile_content = '''FROM python:3.11-slim

# Install Java
RUN apt-get update && apt-get install -y \\
    openjdk-11-jdk \\
    && rm -rf /var/lib/apt/lists/*

# Set JAVA_HOME
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

# Install PyTerrier
RUN pip install python-terrier pandas numpy

# Set working directory
WORKDIR /app

# Copy your code
COPY . .

# Run your script
CMD ["python", "pyterrier_search_demo.py"]
'''
        
        print("Create a Dockerfile:")
        with open("Dockerfile", 'w') as f:
            f.write(dockerfile_content)
        
        print("✅ Dockerfile created")
        print()
        print("Build and run:")
        print("docker build -t pyterrier-demo .")
        print("docker run -it pyterrier-demo")
    
    def solution_4_alternative_libraries(self):
        """Solution 4: Use alternative libraries"""
        print("\n🔧 Solution 4: Alternative Libraries")
        print("=" * 50)
        
        print("If PyTerrier doesn't work, consider these alternatives:")
        print()
        print("1. 🔍 Whoosh (Pure Python):")
        print("   pip install whoosh")
        print("   # Lightweight, pure Python search library")
        print()
        print("2. 🚀 Elasticsearch:")
        print("   pip install elasticsearch")
        print("   # Requires Elasticsearch server")
        print()
        print("3. 📚 Rank-BM25:")
        print("   pip install rank-bm25")
        print("   # Simple BM25 implementation")
        print()
        print("4. 🧠 Sentence Transformers + FAISS:")
        print("   pip install sentence-transformers faiss-cpu")
        print("   # For semantic search")
    
    def create_alternative_demo(self):
        """Create alternative demo using pure Python libraries"""
        print("\n🔧 Creating Alternative Demo")
        print("=" * 50)
        
        alternative_code = '''#!/usr/bin/env python3
"""
Alternative Search Demo using Pure Python Libraries

This demo uses rank-bm25 and other pure Python libraries
as an alternative to PyTerrier when installation fails.
"""

import json
import pandas as pd
from typing import List, Dict
from collections import Counter
import math

try:
    from rank_bm25 import BM25Okapi
    BM25_AVAILABLE = True
except ImportError:
    BM25_AVAILABLE = False
    print("⚠️  rank-bm25 not available. Install with: pip install rank-bm25")


class AlternativeSearchDemo:
    """Alternative search demo using pure Python"""
    
    def __init__(self):
        self.documents = []
        self.bm25 = None
        self.doc_tokens = []
    
    def create_documents(self):
        """Create sample documents"""
        self.documents = [
            {
                "docno": "tech001",
                "title": "Introduction to Machine Learning",
                "text": "Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed.",
                "category": "Technology"
            },
            {
                "docno": "tech002", 
                "title": "Deep Learning Neural Networks",
                "text": "Deep learning uses artificial neural networks with multiple layers to model and understand complex patterns in data.",
                "category": "Technology"
            },
            {
                "docno": "health001",
                "title": "Benefits of Regular Exercise",
                "text": "Regular physical exercise provides numerous health benefits including improved cardiovascular health and stronger muscles.",
                "category": "Health"
            },
            {
                "docno": "science001",
                "title": "Climate Change Impact",
                "text": "Climate change refers to long-term shifts in global temperatures and weather patterns caused by human activities.",
                "category": "Science"
            },
            {
                "docno": "travel001",
                "title": "European City Adventures",
                "text": "Europe offers incredible diversity in culture, architecture, and cuisine from romantic Venice to historic Prague.",
                "category": "Travel"
            }
        ]
        
        print(f"✅ Created {len(self.documents)} documents")
    
    def build_index(self):
        """Build BM25 index"""
        if not BM25_AVAILABLE:
            print("❌ BM25 not available, using simple keyword matching")
            return False
        
        # Tokenize documents
        self.doc_tokens = []
        for doc in self.documents:
            text = f"{doc['title']} {doc['text']}"
            tokens = text.lower().split()
            self.doc_tokens.append(tokens)
        
        # Build BM25 index
        self.bm25 = BM25Okapi(self.doc_tokens)
        print("✅ BM25 index built")
        return True
    
    def search(self, query: str, top_k: int = 5):
        """Search documents"""
        query_tokens = query.lower().split()
        
        if self.bm25:
            # BM25 search
            scores = self.bm25.get_scores(query_tokens)
            doc_scores = [(i, score) for i, score in enumerate(scores)]
            doc_scores.sort(key=lambda x: x[1], reverse=True)
            
            results = []
            for i, (doc_idx, score) in enumerate(doc_scores[:top_k]):
                doc = self.documents[doc_idx]
                results.append({
                    'rank': i + 1,
                    'docno': doc['docno'],
                    'title': doc['title'],
                    'score': score,
                    'category': doc['category']
                })
            
            return results
        else:
            # Simple keyword matching
            results = []
            for doc in self.documents:
                text = f"{doc['title']} {doc['text']}".lower()
                score = sum(1 for token in query_tokens if token in text)
                if score > 0:
                    results.append({
                        'rank': 0,
                        'docno': doc['docno'],
                        'title': doc['title'],
                        'score': score,
                        'category': doc['category']
                    })
            
            results.sort(key=lambda x: x['score'], reverse=True)
            for i, result in enumerate(results[:top_k]):
                result['rank'] = i + 1
            
            return results[:top_k]
    
    def interactive_search(self):
        """Interactive search interface"""
        print("\\n🔍 Alternative Search Demo")
        print("=" * 40)
        print("Commands: search query, stats, quit")
        
        while True:
            try:
                user_input = input("\\nSearch> ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'stats':
                    print(f"Documents: {len(self.documents)}")
                    categories = Counter(doc['category'] for doc in self.documents)
                    print(f"Categories: {dict(categories)}")
                elif user_input:
                    results = self.search(user_input)
                    self.display_results(results, user_input)
                    
            except KeyboardInterrupt:
                break
        
        print("\\n👋 Goodbye!")
    
    def display_results(self, results, query):
        """Display search results"""
        print(f"\\n🔍 Results for: '{query}'")
        print("-" * 50)
        
        if not results:
            print("No results found")
            return
        
        for result in results:
            print(f"{result['rank']}. [{result['docno']}] {result['title']}")
            print(f"   Category: {result['category']} | Score: {result['score']:.3f}")
    
    def run_demo(self):
        """Run complete demo"""
        print("🚀 Alternative Search Demo (Pure Python)")
        print("=" * 50)
        
        self.create_documents()
        self.build_index()
        
        # Demo searches
        demo_queries = ["machine learning", "health exercise", "climate change"]
        
        for query in demo_queries:
            results = self.search(query)
            self.display_results(results, query)
        
        # Interactive search
        choice = input("\\nStart interactive search? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            self.interactive_search()


if __name__ == "__main__":
    demo = AlternativeSearchDemo()
    demo.run_demo()
'''
        
        with open("alternative_search_demo.py", 'w') as f:
            f.write(alternative_code)
        
        print("✅ Created alternative_search_demo.py")
        print("\nTo use:")
        print("1. pip install rank-bm25")
        print("2. python alternative_search_demo.py")
    
    def run_complete_fix(self):
        """Run complete fix process"""
        print("🔧 PyTerrier Installation Fixer")
        print("=" * 50)
        
        # Diagnose issues
        issues = self.diagnose_issue()
        
        if issues:
            print(f"\n❌ Found {len(issues)} issues:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
        else:
            print("\n✅ No obvious issues detected")
        
        # Provide solutions
        print("\n🔧 Available Solutions:")
        print("=" * 50)
        
        self.solution_1_compatible_python()
        self.solution_2_precompiled_wheels()
        self.solution_3_docker()
        self.solution_4_alternative_libraries()
        
        # Create alternative demo
        self.create_alternative_demo()
        
        print("\n💡 Recommendations:")
        print("1. Try Solution 1 (Compatible Python) first")
        print("2. If that fails, use Solution 3 (Docker)")
        print("3. For immediate testing, use the alternative demo")
        print("4. Consider alternative libraries for production use")


def main():
    """Main function"""
    fixer = PyTerrierFixer()
    fixer.run_complete_fix()


if __name__ == "__main__":
    main()
