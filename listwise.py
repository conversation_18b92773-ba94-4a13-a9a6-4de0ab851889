import json
import os
import asyncio
import aiohttp
from typing import Dict, List, Tuple, Any
import pandas as pd
import pyterrier as pt
from tqdm import tqdm
import numpy as np
from dotenv import load_dotenv
import logging
import re
from dataclasses import dataclass

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LLMListwiseReranker:
    """Listwise reranking using LLMs"""
    
    def __init__(self, api_key: str = None, base_url: str = None, model: str = "anthropic/claude-3-haiku"):
        """
        Initialize LLM Listwise Reranker
        
        Args:
            api_key: OpenRouter API key
            base_url: OpenRouter base URL
            model: Model to use for reranking
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self.base_url = base_url or os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
        self.model = model
        
        # Configure proxy if available
        self.proxy = os.getenv("HTTP_PROXY") or os.getenv("HTTPS_PROXY")
        
        if not self.api_key:
            raise ValueError("OpenRouter API key not provided")
    
    async def rerank_listwise(self, query: str, documents: List[Dict], window_size: int = 20) -> List[Dict]:
        """
        Perform listwise reranking on documents
        
        Args:
            query: Query text
            documents: List of documents with 'docno', 'text', 'score'
            window_size: Size of the sliding window for reranking
            
        Returns:
            Reranked documents
        """
        if len(documents) <= window_size:
            # Single pass if documents fit in one window
            return await self._rerank_window(query, documents, 0)
        
        # Sliding window approach for large document sets
        return await self._sliding_window_rerank(query, documents, window_size)
    
    async def _sliding_window_rerank(self, query: str, documents: List[Dict], window_size: int) -> List[Dict]:
        """
        Rerank using sliding window approach
        """
        step_size = window_size // 2  # 50% overlap
        reranked_docs = []
        doc_scores = {}  # Track scores across windows
        
        # Process windows
        for i in range(0, len(documents), step_size):
            window = documents[i:i + window_size]
            if len(window) < 5:  # Skip very small windows
                continue
                
            # Rerank this window
            window_ranked = await self._rerank_window(query, window, i)
            
            # Aggregate scores
            for rank, doc in enumerate(window_ranked):
                docno = doc['docno']
                # Use exponential decay for rank aggregation
                score = 1.0 / (rank + 1)
                
                if docno in doc_scores:
                    doc_scores[docno]['score'] += score
                    doc_scores[docno]['count'] += 1
                else:
                    doc_scores[docno] = {
                        'score': score,
                        'count': 1,
                        'doc': doc
                    }
            
            # Small delay to avoid rate limiting
            await asyncio.sleep(0.3)
        
        # Final ranking based on aggregated scores
        final_ranking = []
        for docno, info in doc_scores.items():
            doc = info['doc']
            doc['aggregated_score'] = info['score'] / info['count']
            final_ranking.append(doc)
        
        # Sort by aggregated score
        final_ranking.sort(key=lambda x: x['aggregated_score'], reverse=True)
        
        # Assign final ranks
        for idx, doc in enumerate(final_ranking):
            doc['reranked_rank'] = idx + 1
        
        return final_ranking
    
    async def _rerank_window(self, query: str, window: List[Dict], window_start: int) -> List[Dict]:
        """
        Rerank a single window of documents
        """
        # Create listwise ranking prompt
        prompt = self._create_listwise_prompt(query, window)
        
        # Get ranking from LLM
        ranking_response = await self._call_llm(prompt)
        
        # Parse and apply ranking
        ranked_indices = self._parse_ranking_response(ranking_response, len(window))
        
        # Apply ranking
        reranked_window = []
        for rank, idx in enumerate(ranked_indices):
            if 0 <= idx < len(window):
                doc = window[idx].copy()
                doc['window_rank'] = rank + 1
                doc['window_start'] = window_start
                reranked_window.append(doc)
        
        # Add any missing documents at the end
        included_indices = set(ranked_indices)
        for idx, doc in enumerate(window):
            if idx not in included_indices:
                doc = doc.copy()
                doc['window_rank'] = len(reranked_window) + 1
                doc['window_start'] = window_start
                reranked_window.append(doc)
        
        return reranked_window
    
    def _create_listwise_prompt(self, query: str, documents: List[Dict]) -> str:
        """
        Create prompt for listwise ranking
        """
        prompt = f"""Task: Rank the following passages by their relevance to the query.

Query: "{query}"

Passages to rank:
"""
        
        for i, doc in enumerate(documents):
            # Truncate text for prompt
            text = doc['text'][:250] + "..." if len(doc['text']) > 250 else doc['text']
            text = text.replace('\n', ' ').strip()
            prompt += f"\n[{i+1}] {text}\n"
        
        prompt += """
Instructions:
1. Consider how well each passage answers the query
2. Prioritize passages with direct, complete answers
3. Consider information quality and relevance
4. Return ONLY a comma-separated list of passage numbers in order of relevance (most to least relevant)

Example format: 3,1,5,2,4

Ranking:"""
        
        return prompt
    
    async def _call_llm(self, prompt: str) -> str:
        """
        Call the LLM API
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        
        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a search ranking expert. Rank passages by relevance to queries. Return only comma-separated numbers."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        # Configure connector with proxy if needed
        connector = None
        if self.proxy:
            connector = aiohttp.TCPConnector(force_close=True)
        
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(connector=connector) as session:
            try:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    proxy=self.proxy if self.proxy else None,
                    timeout=timeout
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"API Error {response.status}: {error_text}")
                        return ""
                    
                    result = await response.json()
                    content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                    return content.strip()
                    
            except asyncio.TimeoutError:
                logger.error("Request timeout")
                return ""
            except Exception as e:
                logger.error(f"Error during API call: {e}")
                return ""
    
    def _parse_ranking_response(self, response: str, num_docs: int) -> List[int]:
        """
        Parse ranking from LLM response
        """
        # Clean response
        response = response.strip()
        
        # Try to extract comma-separated numbers
        # Remove any non-digit characters except commas
        cleaned = re.sub(r'[^\d,]', '', response)
        
        if not cleaned:
            logger.warning(f"Failed to parse ranking from: {response}")
            return list(range(num_docs))
        
        # Split and parse numbers
        try:
            numbers = [int(n.strip()) - 1 for n in cleaned.split(',') if n.strip()]
            
            # Validate numbers
            valid_numbers = []
            seen = set()
            
            for n in numbers:
                if 0 <= n < num_docs and n not in seen:
                    valid_numbers.append(n)
                    seen.add(n)
            
            # Add missing indices at the end
            for i in range(num_docs):
                if i not in seen:
                    valid_numbers.append(i)
            
            return valid_numbers[:num_docs]
            
        except Exception as e:
            logger.warning(f"Error parsing ranking: {e}, response: {response}")
            return list(range(num_docs))

class DL19ListwiseReranker:
    """Main pipeline for DL19 listwise reranking"""
    
    def __init__(self, results_file: str, llm_reranker: LLMListwiseReranker):
        self.results_file = results_file
        self.llm_reranker = llm_reranker
        self.results_data = None
        self.reranked_results = {}
        
        if not pt.started():
            pt.init()
    
    def load_results(self):
        """Load BM25 results from JSON"""
        logger.info(f"Loading results from {self.results_file}")
        with open(self.results_file, 'r', encoding='utf-8') as f:
            self.results_data = json.load(f)
        
        logger.info(f"Loaded results for {len(self.results_data['queries'])} queries")
    
    async def rerank_all_queries(self, max_queries: int = None, window_size: int = 20):
        """
        Rerank all queries using listwise LLM ranking
        """
        queries = list(self.results_data['queries'].items())
        if max_queries:
            queries = queries[:max_queries]
        
        logger.info(f"Starting listwise reranking for {len(queries)} queries...")
        
        for qid, query_data in tqdm(queries, desc="Reranking queries"):
            query_text = query_data['query_text']
            documents = query_data['documents']
            
            # Prepare documents
            docs_for_rerank = [
                {
                    'docno': doc['docno'],
                    'text': doc['text'],
                    'score': doc['score'],
                    'original_rank': doc['rank']
                }
                for doc in documents
            ]
            
            try:
                # Perform listwise reranking
                reranked = await self.llm_reranker.rerank_listwise(
                    query_text, 
                    docs_for_rerank,
                    window_size=window_size
                )
                
                self.reranked_results[qid] = {
                    'query_text': query_text,
                    'documents': reranked
                }
                
                logger.info(f"Completed reranking for query {qid}")
                
            except Exception as e:
                logger.error(f"Error reranking query {qid}: {e}")
                # Keep original ranking on error
                for i, doc in enumerate(docs_for_rerank):
                    doc['reranked_rank'] = i + 1
                
                self.reranked_results[qid] = {
                    'query_text': query_text,
                    'documents': docs_for_rerank
                }
    
    def save_reranked_results(self, output_file: str):
        """Save reranked results to JSON"""
        output_data = {
            "method": "BM25+LLM_Listwise_Rerank",
            "base_method": self.results_data.get("method", "BM25"),
            "reranking_model": self.llm_reranker.model,
            "reranking_approach": "listwise",
            "queries": self.reranked_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved reranked results to {output_file}")
    
    def convert_to_trec_format(self) -> pd.DataFrame:
        """Convert reranked results to TREC format for evaluation"""
        rows = []
        
        for qid, query_data in self.reranked_results.items():
            for doc in query_data['documents']:
                rows.append({
                    'qid': qid,
                    'docno': doc['docno'],
                    'rank': doc.get('reranked_rank', doc.get('original_rank', 0)),
                    'score': doc.get('aggregated_score', doc.get('score', 0))
                })
        
        return pd.DataFrame(rows)

def evaluate_and_compare(baseline_file: str, reranked_df: pd.DataFrame):
    """Evaluate and compare with baseline"""
    
    # Load DL19 qrels
    dataset = pt.get_dataset("irds:msmarco-passage/trec-dl-2019/judged")
    qrels = dataset.get_qrels()
    
    # Load baseline
    with open(baseline_file, 'r') as f:
        baseline_data = json.load(f)
    
    # Convert baseline to DataFrame
    baseline_rows = []
    for qid, query_data in baseline_data['queries'].items():
        # Only include queries that were reranked
        if qid in reranked_df['qid'].unique():
            for doc in query_data['documents']:
                baseline_rows.append({
                    'qid': qid,
                    'docno': doc['docno'],
                    'rank': doc['rank'],
                    'score': doc['score']
                })
    
    baseline_df = pd.DataFrame(baseline_rows)
    
    # Evaluation metrics
    metrics = ["map", "ndcg", "ndcg_cut_10", "ndcg_cut_20", "P_10", "P_20", "recall_100", "mrt"]
    
    # Evaluate both
    baseline_metrics = pt.Utils.evaluate(baseline_df, qrels, metrics=metrics)
    reranked_metrics = pt.Utils.evaluate(reranked_df, qrels, metrics=metrics)
    
    # Create comparison
    comparison = pd.DataFrame({
        'BM25': baseline_metrics,
        'BM25+LLM_Listwise': reranked_metrics,
        'Improvement': reranked_metrics - baseline_metrics,
        'Improvement%': ((reranked_metrics - baseline_metrics) / baseline_metrics * 100).round(2)
    })
    
    return comparison

async def main():
    """Main execution function"""
    # Configuration
    BM25_RESULTS_FILE = "dl19_bm25_top100_with_text.json"
    OUTPUT_DIR = "./listwise_reranking_results"
    
    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Test API connection first
    logger.info("Testing API connection...")
    test_reranker = LLMListwiseReranker(
        model="anthropic/claude-3-haiku"  # Can also use "openai/gpt-4", "meta-llama/llama-3-70b-instruct"
    )
    
    test_response = await test_reranker._call_llm("Test connection. Respond with 'OK'.")
    if not test_response:
        logger.error("Failed to connect to API. Please check your API key and proxy settings.")
        return
    
    logger.info(f"API connection successful. Response: {test_response}")
    
    # Initialize DL19 reranker
    reranker = DL19ListwiseReranker(BM25_RESULTS_FILE, test_reranker)
    
    # Load results
    reranker.load_results()
    
    # Perform listwise reranking
    await reranker.rerank_all_queries(
        max_queries=2,  # Start with just 2 queries for testing
        window_size=10   # Process 10 documents at a time for testing
    )
    
    # Save results
    output_file = os.path.join(OUTPUT_DIR, "dl19_listwise_reranked.json")
    reranker.save_reranked_results(output_file)
    
    # Convert to evaluation format
    reranked_df = reranker.convert_to_trec_format()
    
    # Evaluate and compare
    comparison = evaluate_and_compare(BM25_RESULTS_FILE, reranked_df)
    
    # Print results
    print("\n" + "="*70)
    print("LISTWISE RERANKING EVALUATION RESULTS")
    print("="*70)
    print(comparison)
    
    # Save evaluation
    comparison.to_csv(os.path.join(OUTPUT_DIR, "listwise_evaluation_comparison.csv"))
    
    # Save run file
    pt.io.write_results(
        reranked_df,
        os.path.join(OUTPUT_DIR, "dl19_listwise_reranked.txt"),
        format="trec"
    )
    
    print(f"\nResults saved to {OUTPUT_DIR}")

if __name__ == "__main__":
    # Run with proper error handling
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
