#!/usr/bin/env python3
"""
LLM4Ranking 快速开始示例

这是一个简化的示例，展示如何快速使用 LLM4Ranking 进行文档重排
"""

import os
from typing import List, Tuple
from llm4ranking import Reranker


def create_reranker(
    approach: str = "rankgpt",
    model: str = "openai/gpt-4-1106-preview",
    api_key: str = None
) -> Reranker:
    """
    创建重排器的便捷函数
    
    Args:
        approach: 重排方法 ("rankgpt", "rel-gen", "query-gen", "prp-heap")
        model: 模型名称
        api_key: API 密钥（如果未提供，将从环境变量读取）
    
    Returns:
        配置好的 Reranker 实例
    """
    # 获取 API 密钥
    if api_key is None:
        api_key = os.getenv("OPENROUTER_API_KEY") or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("请设置 OPENROUTER_API_KEY 或 OPENAI_API_KEY 环境变量")
    
    # 设置代理（如果需要）
    proxy_config = None
    if os.getenv("HTTP_PROXY"):
        proxy_config = {
            "http": os.getenv("HTTP_PROXY"),
            "https": os.getenv("HTTPS_PROXY", os.getenv("HTTP_PROXY"))
        }
    
    # 模型参数
    model_args = {
        "model": model,
        "api_key": api_key,
        "base_url": os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
    }
    
    if proxy_config:
        model_args["proxy_config"] = proxy_config
    
    # 重排参数（自动优化）
    if approach in ["rankgpt", "first"]:
        reranking_args = {"window_size": 20, "step": 10, "truncate_length": 300}
    elif approach in ["prp-heap", "prp-allpair", "prp-bubble"]:
        reranking_args = {"topk": 20, "truncate_length": 300}
    else:
        reranking_args = {"truncate_length": 300}
    
    # 模型前向参数（自动过滤不支持的参数）
    model_fw_args = {
        "max_new_tokens": 120,
        "do_sample": False,
    }
    
    return Reranker(
        reranking_approach=approach,
        model_type="openrouter",  # 推荐使用 openrouter
        model_name=model,
        model_args=model_args,
        reranking_args=reranking_args,
        model_fw_args=model_fw_args
    )


def simple_rerank(
    query: str,
    candidates: List[str],
    approach: str = "rankgpt",
    model: str = "openai/gpt-4-1106-preview"
) -> Tuple[List[str], List[int]]:
    """
    简单的重排函数
    
    Args:
        query: 查询文本
        candidates: 候选文档列表
        approach: 重排方法
        model: 模型名称
    
    Returns:
        (重排后的文档列表, 重排索引)
    """
    try:
        reranker = create_reranker(approach=approach, model=model)
        
        reranked_docs, indices = reranker.rerank(
            query=query,
            candidates=candidates,
            return_indices=True
        )
        
        return reranked_docs, indices
        
    except Exception as e:
        print(f"重排失败: {e}")
        print("返回原始顺序")
        return candidates, list(range(len(candidates)))


# ============================================================================
# 使用示例
# ============================================================================

if __name__ == "__main__":
    # 示例 1: 基础使用
    print("🚀 示例 1: 基础重排")
    print("-" * 40)
    
    query = "什么是机器学习"
    documents = [
        "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习。",
        "深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。",
        "Python是一种流行的编程语言，广泛用于数据科学和机器学习。",
        "监督学习是机器学习的一种类型，使用标记的训练数据来训练模型。",
        "无监督学习不使用标记数据，而是寻找数据中的隐藏模式。"
    ]
    
    try:
        # 方法 1: 使用便捷函数
        reranked_docs, indices = simple_rerank(
            query=query,
            candidates=documents,
            approach="rankgpt"
        )
        
        print(f"查询: {query}")
        print("\n重排结果:")
        for i, (doc, original_idx) in enumerate(zip(reranked_docs[:3], indices[:3])):
            print(f"  {i+1}. [原始位置 #{original_idx+1}] {doc}")
        
    except Exception as e:
        print(f"示例 1 失败: {e}")
    
    print("\n" + "=" * 60)
    
    # 示例 2: 高级使用
    print("🔬 示例 2: 高级配置")
    print("-" * 40)
    
    try:
        # 方法 2: 直接使用 Reranker 类
        reranker = create_reranker(
            approach="rel-gen",  # 尝试不同的方法
            model="openai/gpt-4-1106-preview"
        )
        
        # 执行重排
        reranked_docs, indices = reranker.rerank(
            query="机器学习算法",
            candidates=documents,
            return_indices=True
        )
        
        print("使用 RelevanceGeneration 方法:")
        for i, (doc, original_idx) in enumerate(zip(reranked_docs[:3], indices[:3])):
            position_change = original_idx - i
            change_emoji = "📈" if position_change > 0 else "📉" if position_change < 0 else "➡️"
            print(f"  {i+1}. {change_emoji} [#{original_idx+1}→#{i+1}] {doc[:50]}...")
        
    except Exception as e:
        print(f"示例 2 失败: {e}")
    
    print("\n" + "=" * 60)
    
    # 示例 3: 批量处理
    print("📦 示例 3: 批量重排")
    print("-" * 40)
    
    queries = [
        "什么是深度学习",
        "Python编程语言",
        "监督学习方法"
    ]
    
    try:
        reranker = create_reranker()
        
        for i, query in enumerate(queries):
            print(f"\n查询 {i+1}: {query}")
            
            reranked_docs, _ = reranker.rerank(
                query=query,
                candidates=documents[:3],  # 只用前3个文档
                return_indices=True
            )
            
            print(f"  最相关: {reranked_docs[0][:60]}...")
            
    except Exception as e:
        print(f"示例 3 失败: {e}")
    
    print("\n✅ 演示完成!")
    print("\n💡 提示:")
    print("  1. 确保设置了 OPENROUTER_API_KEY 环境变量")
    print("  2. 如果在国内，可能需要设置 HTTP_PROXY 环境变量")
    print("  3. 尝试不同的重排方法: 'rankgpt', 'rel-gen', 'prp-heap'")
    print("  4. 根据需要调整模型: 'openai/gpt-4', 'anthropic/claude-3-sonnet'")
