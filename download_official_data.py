#!/usr/bin/env python3
"""
下载官方 TREC DL 2019 数据

下载官方的 baseline runs 和相关数据，用于公平的 LLM 重排评估
"""

import os
import requests
from typing import Dict, List, Tuple
import gzip
import json


class OfficialDataDownloader:
    """官方数据下载器"""
    
    def __init__(self, data_dir: str = "official_data"):
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
    
    def download_file(self, url: str, filename: str, description: str = "") -> bool:
        """下载文件的通用方法"""
        filepath = os.path.join(self.data_dir, filename)
        
        if os.path.exists(filepath):
            print(f"✅ {description} already exists: {filepath}")
            return True
        
        try:
            print(f"🔄 Downloading {description}...")
            print(f"   URL: {url}")
            
            response = requests.get(url, timeout=120, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = downloaded / total_size * 100
                            print(f"\r   Progress: {progress:.1f}%", end="", flush=True)
            
            print(f"\n✅ Downloaded {description}: {filepath}")
            print(f"   Size: {downloaded:,} bytes")
            return True
            
        except Exception as e:
            print(f"\n❌ Failed to download {description}: {e}")
            return False
    
    def download_qrels(self) -> bool:
        """下载官方 qrels"""
        return self.download_file(
            "https://trec.nist.gov/data/deep/2019qrels-docs.txt",
            "2019qrels-docs.txt",
            "TREC DL 2019 qrels"
        )
    
    def download_queries(self) -> bool:
        """下载官方查询"""
        return self.download_file(
            "https://trec.nist.gov/data/deep/msmarco-test2019-queries.tsv",
            "msmarco-test2019-queries.tsv",
            "TREC DL 2019 queries"
        )
    
    def download_baseline_runs(self) -> bool:
        """下载官方 baseline runs"""
        
        # BM25 baseline
        bm25_success = self.download_file(
            "https://trec.nist.gov/data/deep/2019_bm25_baseline.txt",
            "2019_bm25_baseline.txt",
            "BM25 baseline run"
        )
        
        # 如果上面的链接不工作，尝试其他可能的链接
        if not bm25_success:
            print("🔄 Trying alternative BM25 baseline source...")
            bm25_success = self.download_file(
                "https://github.com/microsoft/MSMARCO-Document-Ranking/raw/master/baselines/bm25_doc_baseline.txt",
                "bm25_doc_baseline.txt",
                "Alternative BM25 baseline"
            )
        
        return bm25_success
    
    def download_document_collection_info(self) -> bool:
        """下载文档集合信息"""
        
        # 尝试下载文档集合的元数据
        success = self.download_file(
            "https://msmarco.blob.core.windows.net/msmarcoranking/msmarco-docs.tsv.gz",
            "msmarco-docs.tsv.gz",
            "MS MARCO document collection metadata"
        )
        
        if success:
            # 解压文件
            try:
                gz_path = os.path.join(self.data_dir, "msmarco-docs.tsv.gz")
                tsv_path = os.path.join(self.data_dir, "msmarco-docs.tsv")
                
                if not os.path.exists(tsv_path):
                    print("🔄 Extracting document collection...")
                    with gzip.open(gz_path, 'rt', encoding='utf-8') as f_in:
                        with open(tsv_path, 'w', encoding='utf-8') as f_out:
                            f_out.write(f_in.read())
                    print("✅ Extracted document collection")
                
            except Exception as e:
                print(f"⚠️  Failed to extract document collection: {e}")
        
        return success
    
    def parse_queries(self) -> Dict[str, str]:
        """解析查询文件"""
        queries_file = os.path.join(self.data_dir, "msmarco-test2019-queries.tsv")
        
        if not os.path.exists(queries_file):
            print("❌ Queries file not found")
            return {}
        
        queries = {}
        try:
            with open(queries_file, 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        qid, query = parts[0], parts[1]
                        queries[qid] = query
            
            print(f"✅ Parsed {len(queries)} queries")
            return queries
            
        except Exception as e:
            print(f"❌ Error parsing queries: {e}")
            return {}
    
    def parse_baseline_run(self, filename: str = "2019_bm25_baseline.txt") -> Dict[str, List[Tuple[str, float]]]:
        """解析 baseline run 文件"""
        run_file = os.path.join(self.data_dir, filename)
        
        if not os.path.exists(run_file):
            # 尝试备用文件名
            alt_file = os.path.join(self.data_dir, "bm25_doc_baseline.txt")
            if os.path.exists(alt_file):
                run_file = alt_file
            else:
                print(f"❌ Baseline run file not found: {filename}")
                return {}
        
        run_results = {}
        try:
            with open(run_file, 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 6:
                        qid, _, docid, rank, score, _ = parts[:6]
                        score = float(score)
                        
                        if qid not in run_results:
                            run_results[qid] = []
                        
                        run_results[qid].append((docid, score))
            
            # 按分数排序
            for qid in run_results:
                run_results[qid].sort(key=lambda x: x[1], reverse=True)
            
            print(f"✅ Parsed baseline run for {len(run_results)} queries")
            return run_results
            
        except Exception as e:
            print(f"❌ Error parsing baseline run: {e}")
            return {}
    
    def create_demo_data_from_official(self, top_k: int = 20) -> bool:
        """从官方数据创建演示数据"""
        
        print(f"🔄 Creating demo data from official sources...")
        
        # 解析数据
        queries = self.parse_queries()
        baseline_run = self.parse_baseline_run()
        
        if not queries or not baseline_run:
            print("❌ Cannot create demo data without queries and baseline run")
            return False
        
        # 创建演示数据格式
        demo_data = []
        
        common_queries = set(queries.keys()) & set(baseline_run.keys())
        print(f"📊 Found {len(common_queries)} common queries")
        
        for qid in sorted(list(common_queries))[:10]:  # 限制为前10个查询
            query_text = queries[qid]
            docs = baseline_run[qid][:top_k]
            
            hits = []
            for rank, (docid, score) in enumerate(docs):
                hits.append({
                    "qid": int(qid),
                    "docid": docid,
                    "rank": rank + 1,
                    "score": score,
                    "content": f"[Document {docid}] Content would be loaded from MS MARCO collection..."
                })
            
            demo_data.append({
                "query": query_text,
                "hits": hits
            })
        
        # 保存演示数据
        output_file = "official_dl19_demo.jsonl"
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in demo_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"✅ Created demo data: {output_file}")
        print(f"📊 {len(demo_data)} queries, {top_k} documents per query")
        
        return True
    
    def download_all(self) -> bool:
        """下载所有必要的官方数据"""
        
        print("🚀 Downloading Official TREC DL 2019 Data")
        print("=" * 50)
        
        success_count = 0
        total_count = 0
        
        # 下载 qrels
        total_count += 1
        if self.download_qrels():
            success_count += 1
        
        # 下载查询
        total_count += 1
        if self.download_queries():
            success_count += 1
        
        # 下载 baseline runs
        total_count += 1
        if self.download_baseline_runs():
            success_count += 1
        
        # 下载文档集合信息（可选）
        total_count += 1
        if self.download_document_collection_info():
            success_count += 1
        
        print(f"\n📊 Download Summary:")
        print(f"   Successful: {success_count}/{total_count}")
        print(f"   Success rate: {success_count/total_count*100:.1f}%")
        
        if success_count >= 3:  # qrels, queries, baseline
            print("✅ Sufficient data downloaded for evaluation")
            
            # 创建演示数据
            self.create_demo_data_from_official()
            
            return True
        else:
            print("❌ Insufficient data for evaluation")
            return False


def main():
    """主函数"""
    
    print("📥 Official TREC DL 2019 Data Downloader")
    print("=" * 50)
    print("This script downloads official TREC Deep Learning 2019 data")
    print("for fair evaluation of LLM reranking performance.")
    print()
    
    downloader = OfficialDataDownloader()
    
    if downloader.download_all():
        print("\n🎉 Success! Official data downloaded and processed.")
        print("\n📚 Next steps:")
        print("   1. Use official_dl19_demo.jsonl for fair evaluation")
        print("   2. Run evaluation with official qrels")
        print("   3. Compare results with previous dummy qrels evaluation")
        
        print(f"\n📁 Downloaded files in '{downloader.data_dir}':")
        for filename in os.listdir(downloader.data_dir):
            filepath = os.path.join(downloader.data_dir, filename)
            size = os.path.getsize(filepath)
            print(f"   {filename} ({size:,} bytes)")
    
    else:
        print("\n❌ Download failed. Please check network connection and try again.")
        print("\n💡 Alternative approaches:")
        print("   1. Manual download from TREC website")
        print("   2. Use existing toy data with awareness of limitations")
        print("   3. Focus on methodology demonstration rather than absolute performance")


if __name__ == "__main__":
    main()
