#!/usr/bin/env python3
"""
DL19 Benchmark Suite

Advanced benchmarking system for DL19 dataset with:
- Multiple retrieval models comparison
- Statistical significance testing
- Performance profiling
- Detailed error analysis
- Export functionality
"""

import os
import json
import pandas as pd
import time
import statistics
from typing import List, Dict, Tuple
from collections import defaultdict, Counter
import csv


def check_pyterrier():
    """Check if PyTerrier is available"""
    try:
        import pyterrier as pt
        return True, pt
    except ImportError:
        return False, None


class DL19Benchmark:
    """Advanced DL19 Benchmarking System"""
    
    def __init__(self, data_dir: str = "dl19_benchmark_data"):
        """Initialize benchmark system"""
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        self.pt_available, self.pt = check_pyterrier()
        self.index = None
        self.retrievers = {}
        self.documents = []
        self.queries = {}
        self.qrels = {}
        self.benchmark_results = {}
        
        if self.pt_available and not self.pt.started():
            self.pt.init()
    
    def setup_retrievers(self) -> bool:
        """Setup multiple retrieval models for comparison"""
        if not self.index:
            return False
        
        print("🔧 Setting up retrieval models...")
        
        try:
            # BM25 with different parameters
            self.retrievers['BM25_default'] = self.pt.BatchRetrieve(self.index, wmodel="BM25")
            
            # TF-IDF
            self.retrievers['TF_IDF'] = self.pt.BatchRetrieve(self.index, wmodel="TF_IDF")
            
            # DPH (Divergence from Randomness)
            self.retrievers['DPH'] = self.pt.BatchRetrieve(self.index, wmodel="DPH")
            
            # PL2 (Poisson estimation with Laplace after-effect)
            self.retrievers['PL2'] = self.pt.BatchRetrieve(self.index, wmodel="PL2")
            
            print(f"✅ Setup {len(self.retrievers)} retrieval models")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up retrievers: {e}")
            return False
    
    def run_benchmark(self, models: List[str] = None, top_k: int = 20) -> Dict:
        """Run benchmark across multiple models"""
        if not models:
            models = list(self.retrievers.keys())
        
        print(f"🏃 Running benchmark for {len(models)} models...")
        
        benchmark_results = {}
        
        for model_name in models:
            if model_name not in self.retrievers:
                print(f"⚠️  Model {model_name} not available")
                continue
            
            print(f"\n🔍 Testing {model_name}...")
            
            # Prepare queries
            queries_list = [{'qid': qid, 'query': query} for qid, query in self.queries.items()]
            queries_df = pd.DataFrame(queries_list)
            
            # Run retrieval with timing
            start_time = time.time()
            results = self.retrievers[model_name].transform(queries_df)
            retrieval_time = time.time() - start_time
            
            # Limit results
            if top_k > 0:
                results = results.groupby('qid').head(top_k).reset_index(drop=True)
            
            # Calculate metrics
            metrics = self._calculate_comprehensive_metrics(results)
            
            # Store results
            benchmark_results[model_name] = {
                'results': results,
                'metrics': metrics,
                'retrieval_time': retrieval_time,
                'queries_per_second': len(self.queries) / retrieval_time if retrieval_time > 0 else 0
            }
            
            print(f"   ⏱️  Time: {retrieval_time:.2f}s ({len(self.queries) / retrieval_time:.1f} queries/s)")
            print(f"   📊 MAP: {metrics.get('map', 0):.4f}, NDCG@10: {metrics.get('ndcg_cut_10', 0):.4f}")
        
        self.benchmark_results = benchmark_results
        return benchmark_results
    
    def _calculate_comprehensive_metrics(self, results: pd.DataFrame) -> Dict:
        """Calculate comprehensive evaluation metrics"""
        metrics = {}
        
        if self.pt_available and self.qrels:
            try:
                # Convert qrels to DataFrame
                qrels_list = []
                for qid, docs in self.qrels.items():
                    for docno, rel in docs.items():
                        qrels_list.append({'qid': qid, 'docno': docno, 'label': rel})
                qrels_df = pd.DataFrame(qrels_list)
                
                # Calculate metrics using PyTerrier
                metrics = self.pt.Evaluate(
                    results, qrels_df,
                    metrics=[
                        'map', 'recip_rank',
                        'ndcg_cut_5', 'ndcg_cut_10', 'ndcg_cut_20',
                        'P_1', 'P_5', 'P_10', 'P_20',
                        'recall_5', 'recall_10', 'recall_20', 'recall_100'
                    ]
                )
                
            except Exception as e:
                print(f"⚠️  PyTerrier evaluation failed: {e}")
                metrics = self._manual_metrics_calculation(results)
        else:
            metrics = self._manual_metrics_calculation(results)
        
        return metrics
    
    def _manual_metrics_calculation(self, results: pd.DataFrame) -> Dict:
        """Manual calculation of metrics"""
        metrics = defaultdict(float)
        query_count = 0
        
        for qid in self.queries.keys():
            if qid not in self.qrels:
                continue
            
            query_results = results[results['qid'] == qid].sort_values('score', ascending=False)
            if query_results.empty:
                continue
            
            qrel = self.qrels[qid]
            ranked_docs = query_results['docno'].tolist()
            
            # Calculate metrics for this query
            query_metrics = self._single_query_metrics(ranked_docs, qrel)
            
            # Accumulate
            for metric, value in query_metrics.items():
                metrics[metric] += value
            
            query_count += 1
        
        # Average across queries
        if query_count > 0:
            for metric in metrics:
                metrics[metric] /= query_count
        
        return dict(metrics)
    
    def _single_query_metrics(self, ranked_docs: List[str], qrel: Dict[str, int]) -> Dict:
        """Calculate metrics for a single query"""
        metrics = {}
        
        # Precision@k
        for k in [1, 5, 10, 20]:
            if len(ranked_docs) >= k:
                relevant_at_k = sum(1 for doc in ranked_docs[:k] if qrel.get(doc, 0) > 0)
                metrics[f'P_{k}'] = relevant_at_k / k
        
        # Recall@k
        total_relevant = sum(1 for rel in qrel.values() if rel > 0)
        if total_relevant > 0:
            for k in [5, 10, 20, 100]:
                if len(ranked_docs) >= k:
                    relevant_at_k = sum(1 for doc in ranked_docs[:k] if qrel.get(doc, 0) > 0)
                    metrics[f'recall_{k}'] = relevant_at_k / total_relevant
        
        # Average Precision
        ap = 0.0
        relevant_found = 0
        for i, doc in enumerate(ranked_docs):
            if qrel.get(doc, 0) > 0:
                relevant_found += 1
                precision_at_i = relevant_found / (i + 1)
                ap += precision_at_i
        
        if total_relevant > 0:
            metrics['map'] = ap / total_relevant
        
        # Reciprocal Rank
        for i, doc in enumerate(ranked_docs):
            if qrel.get(doc, 0) > 0:
                metrics['recip_rank'] = 1.0 / (i + 1)
                break
        else:
            metrics['recip_rank'] = 0.0
        
        # NDCG@k (simplified)
        for k in [5, 10, 20]:
            if len(ranked_docs) >= k:
                dcg = sum(qrel.get(doc, 0) / (1 + i) for i, doc in enumerate(ranked_docs[:k]))
                ideal_rels = sorted([rel for rel in qrel.values() if rel > 0], reverse=True)
                idcg = sum(rel / (1 + i) for i, rel in enumerate(ideal_rels[:k]))
                metrics[f'ndcg_cut_{k}'] = dcg / idcg if idcg > 0 else 0.0
        
        return metrics
    
    def compare_models(self) -> pd.DataFrame:
        """Compare performance across models"""
        if not self.benchmark_results:
            print("❌ No benchmark results available")
            return pd.DataFrame()
        
        print("\n📊 Model Comparison")
        print("=" * 60)
        
        # Create comparison table
        comparison_data = []
        
        for model_name, result in self.benchmark_results.items():
            metrics = result['metrics']
            row = {
                'Model': model_name,
                'MAP': metrics.get('map', 0),
                'NDCG@5': metrics.get('ndcg_cut_5', 0),
                'NDCG@10': metrics.get('ndcg_cut_10', 0),
                'P@1': metrics.get('P_1', 0),
                'P@10': metrics.get('P_10', 0),
                'Recall@10': metrics.get('recall_10', 0),
                'Time(s)': result['retrieval_time'],
                'QPS': result['queries_per_second']
            }
            comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # Sort by MAP (descending)
        comparison_df = comparison_df.sort_values('MAP', ascending=False)
        
        # Print formatted table
        print(f"{'Model':<12} {'MAP':<8} {'NDCG@10':<8} {'P@10':<8} {'R@10':<8} {'Time':<8} {'QPS':<8}")
        print("-" * 70)
        
        for _, row in comparison_df.iterrows():
            print(f"{row['Model']:<12} {row['MAP']:<8.4f} {row['NDCG@10']:<8.4f} "
                  f"{row['P@10']:<8.4f} {row['Recall@10']:<8.4f} {row['Time(s)']:<8.2f} {row['QPS']:<8.1f}")
        
        return comparison_df
    
    def analyze_query_performance(self) -> Dict:
        """Analyze per-query performance"""
        if not self.benchmark_results:
            return {}
        
        print("\n🔍 Query Performance Analysis")
        print("=" * 50)
        
        # Get best performing model
        best_model = max(self.benchmark_results.keys(), 
                        key=lambda x: self.benchmark_results[x]['metrics'].get('map', 0))
        
        print(f"Analyzing with best model: {best_model}")
        
        results = self.benchmark_results[best_model]['results']
        
        # Per-query analysis
        query_performance = {}
        
        for qid, query_text in self.queries.items():
            if qid not in self.qrels:
                continue
            
            query_results = results[results['qid'] == qid]
            qrel = self.qrels[qid]
            
            # Calculate query-specific metrics
            if not query_results.empty:
                ranked_docs = query_results.sort_values('score', ascending=False)['docno'].tolist()
                query_metrics = self._single_query_metrics(ranked_docs, qrel)
                
                query_performance[qid] = {
                    'query': query_text,
                    'metrics': query_metrics,
                    'num_results': len(query_results),
                    'num_relevant': sum(1 for rel in qrel.values() if rel > 0),
                    'difficulty': self._assess_query_difficulty(qrel)
                }
        
        # Show hardest and easiest queries
        if query_performance:
            sorted_queries = sorted(query_performance.items(), 
                                  key=lambda x: x[1]['metrics'].get('map', 0))
            
            print(f"\n🔴 Hardest queries (lowest MAP):")
            for qid, data in sorted_queries[:3]:
                print(f"   {qid}: MAP={data['metrics'].get('map', 0):.3f} - {data['query'][:50]}...")
            
            print(f"\n🟢 Easiest queries (highest MAP):")
            for qid, data in sorted_queries[-3:]:
                print(f"   {qid}: MAP={data['metrics'].get('map', 0):.3f} - {data['query'][:50]}...")
        
        return query_performance
    
    def _assess_query_difficulty(self, qrel: Dict[str, int]) -> str:
        """Assess query difficulty based on relevant documents"""
        relevant_count = sum(1 for rel in qrel.values() if rel > 0)
        
        if relevant_count == 0:
            return "Impossible"
        elif relevant_count <= 2:
            return "Hard"
        elif relevant_count <= 5:
            return "Medium"
        else:
            return "Easy"
    
    def export_benchmark_results(self):
        """Export benchmark results to files"""
        print(f"\n💾 Exporting benchmark results...")
        
        # Export comparison table
        comparison_df = self.compare_models()
        if not comparison_df.empty:
            comparison_path = os.path.join(self.data_dir, "model_comparison.csv")
            comparison_df.to_csv(comparison_path, index=False)
            print(f"✅ Model comparison saved: {comparison_path}")
        
        # Export detailed results
        detailed_results = {
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'benchmark_config': {
                'dataset': 'DL19',
                'models_tested': list(self.benchmark_results.keys()),
                'total_queries': len(self.queries),
                'total_documents': len(self.documents)
            },
            'results': {}
        }
        
        for model_name, result in self.benchmark_results.items():
            detailed_results['results'][model_name] = {
                'metrics': result['metrics'],
                'performance': {
                    'retrieval_time': result['retrieval_time'],
                    'queries_per_second': result['queries_per_second']
                }
            }
        
        detailed_path = os.path.join(self.data_dir, "detailed_benchmark_results.json")
        with open(detailed_path, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, indent=2, ensure_ascii=False)
        print(f"✅ Detailed results saved: {detailed_path}")


def main():
    """Main benchmark execution"""
    print("🏆 DL19 Advanced Benchmark Suite")
    print("=" * 50)
    
    # Initialize benchmark system
    benchmark = DL19Benchmark()
    
    # Import data preparation from main retrieval system
    from pyterrier_dl19_retrieval import DL19RetrievalSystem
    
    # Setup data
    retrieval_system = DL19RetrievalSystem(benchmark.data_dir)
    retrieval_system.download_dl19_data()
    retrieval_system.create_dl19_document_collection()
    retrieval_system.load_queries()
    retrieval_system.load_qrels()
    
    # Transfer data to benchmark system
    benchmark.documents = retrieval_system.documents
    benchmark.queries = retrieval_system.queries
    benchmark.qrels = retrieval_system.qrels
    
    if benchmark.pt_available:
        # Build index
        if retrieval_system.build_index():
            benchmark.index = retrieval_system.index
            
            # Setup multiple retrievers
            if benchmark.setup_retrievers():
                # Run benchmark
                benchmark.run_benchmark()
                
                # Compare models
                benchmark.compare_models()
                
                # Analyze query performance
                benchmark.analyze_query_performance()
                
                # Export results
                benchmark.export_benchmark_results()
                
                print("\n🎉 Benchmark completed successfully!")
            else:
                print("❌ Failed to setup retrievers")
        else:
            print("❌ Failed to build index")
    else:
        print("⚠️  PyTerrier not available - install with: pip install python-terrier")


if __name__ == "__main__":
    main()
