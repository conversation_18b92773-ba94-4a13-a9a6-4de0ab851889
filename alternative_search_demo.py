#!/usr/bin/env python3
"""
Alternative Search Demo using Pure Python Libraries

This demo uses rank-bm25 and other pure Python libraries
as an alternative to PyTerrier when installation fails.
"""

import json
import pandas as pd
from typing import List, Dict
from collections import Counter
import math

try:
    from rank_bm25 import BM25Okapi
    BM25_AVAILABLE = True
except ImportError:
    BM25_AVAILABLE = False
    print("⚠️  rank-bm25 not available. Install with: pip install rank-bm25")


class AlternativeSearchDemo:
    """Alternative search demo using pure Python"""
    
    def __init__(self):
        self.documents = []
        self.bm25 = None
        self.doc_tokens = []
    
    def create_documents(self):
        """Create sample documents"""
        self.documents = [
            {
                "docno": "tech001",
                "title": "Introduction to Machine Learning",
                "text": "Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed.",
                "category": "Technology"
            },
            {
                "docno": "tech002", 
                "title": "Deep Learning Neural Networks",
                "text": "Deep learning uses artificial neural networks with multiple layers to model and understand complex patterns in data.",
                "category": "Technology"
            },
            {
                "docno": "health001",
                "title": "Benefits of Regular Exercise",
                "text": "Regular physical exercise provides numerous health benefits including improved cardiovascular health and stronger muscles.",
                "category": "Health"
            },
            {
                "docno": "science001",
                "title": "Climate Change Impact",
                "text": "Climate change refers to long-term shifts in global temperatures and weather patterns caused by human activities.",
                "category": "Science"
            },
            {
                "docno": "travel001",
                "title": "European City Adventures",
                "text": "Europe offers incredible diversity in culture, architecture, and cuisine from romantic Venice to historic Prague.",
                "category": "Travel"
            }
        ]
        
        print(f"✅ Created {len(self.documents)} documents")
    
    def build_index(self):
        """Build BM25 index"""
        if not BM25_AVAILABLE:
            print("❌ BM25 not available, using simple keyword matching")
            return False
        
        # Tokenize documents
        self.doc_tokens = []
        for doc in self.documents:
            text = f"{doc['title']} {doc['text']}"
            tokens = text.lower().split()
            self.doc_tokens.append(tokens)
        
        # Build BM25 index
        self.bm25 = BM25Okapi(self.doc_tokens)
        print("✅ BM25 index built")
        return True
    
    def search(self, query: str, top_k: int = 5):
        """Search documents"""
        query_tokens = query.lower().split()
        
        if self.bm25:
            # BM25 search
            scores = self.bm25.get_scores(query_tokens)
            doc_scores = [(i, score) for i, score in enumerate(scores)]
            doc_scores.sort(key=lambda x: x[1], reverse=True)
            
            results = []
            for i, (doc_idx, score) in enumerate(doc_scores[:top_k]):
                doc = self.documents[doc_idx]
                results.append({
                    'rank': i + 1,
                    'docno': doc['docno'],
                    'title': doc['title'],
                    'score': score,
                    'category': doc['category']
                })
            
            return results
        else:
            # Simple keyword matching
            results = []
            for doc in self.documents:
                text = f"{doc['title']} {doc['text']}".lower()
                score = sum(1 for token in query_tokens if token in text)
                if score > 0:
                    results.append({
                        'rank': 0,
                        'docno': doc['docno'],
                        'title': doc['title'],
                        'score': score,
                        'category': doc['category']
                    })
            
            results.sort(key=lambda x: x['score'], reverse=True)
            for i, result in enumerate(results[:top_k]):
                result['rank'] = i + 1
            
            return results[:top_k]
    
    def interactive_search(self):
        """Interactive search interface"""
        print("\n🔍 Alternative Search Demo")
        print("=" * 40)
        print("Commands: search query, stats, quit")
        
        while True:
            try:
                user_input = input("\nSearch> ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'stats':
                    print(f"Documents: {len(self.documents)}")
                    categories = Counter(doc['category'] for doc in self.documents)
                    print(f"Categories: {dict(categories)}")
                elif user_input:
                    results = self.search(user_input)
                    self.display_results(results, user_input)
                    
            except KeyboardInterrupt:
                break
        
        print("\n👋 Goodbye!")
    
    def display_results(self, results, query):
        """Display search results"""
        print(f"\n🔍 Results for: '{query}'")
        print("-" * 50)
        
        if not results:
            print("No results found")
            return
        
        for result in results:
            print(f"{result['rank']}. [{result['docno']}] {result['title']}")
            print(f"   Category: {result['category']} | Score: {result['score']:.3f}")
    
    def run_demo(self):
        """Run complete demo"""
        print("🚀 Alternative Search Demo (Pure Python)")
        print("=" * 50)
        
        self.create_documents()
        self.build_index()
        
        # Demo searches
        demo_queries = ["machine learning", "health exercise", "climate change"]
        
        for query in demo_queries:
            results = self.search(query)
            self.display_results(results, query)
        
        # Interactive search
        choice = input("\nStart interactive search? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            self.interactive_search()


if __name__ == "__main__":
    demo = AlternativeSearchDemo()
    demo.run_demo()
