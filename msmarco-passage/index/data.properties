#/Users/<USER>/Documents/augment-projects/ir/msmarco-passage/index/data_stream0_1.properties
#Wed Jul 09 07:49:30 CST 2025
index.created=1752018136944
index.direct-inputstream.class=org.terrier.structures.bit.BitPostingIndexInputStream
index.direct-inputstream.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String,java.util.Iterator,java.lang.Class
index.direct-inputstream.parameter_values=index,structureName,document-inputstream,org.terrier.structures.postings.bit.FieldIterablePosting
index.direct.blocks=0
index.direct.blocks.max=0
index.direct.class=org.terrier.structures.bit.BitPostingIndex
index.direct.fields.count=1
index.direct.fields.names=text
index.direct.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String,java.lang.Class
index.direct.parameter_values=index,structureName,org.terrier.structures.postings.bit.FieldIterablePosting
index.document-factory.class=org.terrier.structures.FieldDocumentIndexEntry$Factory
index.document-factory.parameter_types=java.lang.String
index.document-factory.parameter_values=${index.direct.fields.count}
index.document-inputstream.class=org.terrier.structures.FSADocumentIndex$FSADocumentIndexIterator
index.document-inputstream.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String
index.document-inputstream.parameter_values=index,structureName
index.document.class=org.terrier.structures.FSADocumentIndex
index.document.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String
index.document.parameter_values=index,structureName
index.inverted-inputstream.class=org.terrier.structures.bit.BitPostingIndexInputStream
index.inverted-inputstream.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String,java.util.Iterator,java.lang.Class
index.inverted-inputstream.parameter_values=index,structureName,lexicon-entry-inputstream,org.terrier.structures.postings.bit.FieldIterablePosting
index.inverted.blocks=0
index.inverted.blocks.max=0
index.inverted.class=org.terrier.structures.bit.BitPostingIndex
index.inverted.fields.count=1
index.inverted.fields.names=text
index.inverted.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String,java.lang.Class
index.inverted.parameter_values=index,structureName,org.terrier.structures.postings.bit.FieldIterablePosting
index.lexicon-entry-inputstream.class=org.terrier.structures.FSOMapFileLexicon$MapFileLexiconEntryIterator
index.lexicon-entry-inputstream.parameter_types=java.lang.String,org.terrier.structures.IndexOnDisk
index.lexicon-entry-inputstream.parameter_values=structureName,index
index.lexicon-inputstream.class=org.terrier.structures.FSOMapFileLexicon$MapFileLexiconIterator
index.lexicon-inputstream.parameter_types=java.lang.String,org.terrier.structures.IndexOnDisk
index.lexicon-inputstream.parameter_values=structureName,index
index.lexicon-keyfactory.class=org.terrier.structures.seralization.FixedSizeTextFactory
index.lexicon-keyfactory.parameter_types=java.lang.String
index.lexicon-keyfactory.parameter_values=${max.term.length}
index.lexicon-valuefactory.class=org.terrier.structures.FieldLexiconEntry$Factory
index.lexicon-valuefactory.parameter_types=java.lang.String
index.lexicon-valuefactory.parameter_values=${index.direct.fields.count}
index.lexicon.bsearchshortcut=charmap
index.lexicon.class=org.terrier.structures.FSOMapFileLexicon
index.lexicon.parameter_types=java.lang.String,org.terrier.structures.IndexOnDisk
index.lexicon.parameter_values=structureName,index
index.lexicon.termids=fileinmem
index.meta-inputstream.class=org.terrier.structures.ZstdCompressedMetaIndex$InputStream
index.meta-inputstream.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String
index.meta-inputstream.parameter_values=index,structureName
index.meta.class=org.terrier.structures.ZstdCompressedMetaIndex
index.meta.data-source=file
index.meta.entries=8841823
index.meta.entry-length=12352
index.meta.index-source=fileinmem
index.meta.key-names=docno,text
index.meta.parameter_types=org.terrier.structures.IndexOnDisk,java.lang.String
index.meta.parameter_values=index,structureName
index.meta.reverse-key-names=docno
index.meta.value-lengths=20,4096
index.meta.value-sorted=false,false
index.terrier.version=5.11
max.term.length=20
num.Documents=8841823
num.Pointers=215238456
num.Terms=1170682
num.Tokens=288759529
num.field.0.Tokens=288759529
termpipelines=Stopwords,PorterStemmer
