"""
Configuration module for the Information Retrieval Experiment Platform
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the IR experiment platform"""
    
    # OpenRouter API Configuration
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    OPENROUTER_BASE_URL = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
    
    # Model Configuration
    DEFAULT_MODEL = os.getenv("DEFAULT_MODEL", "openai/gpt-4-1106-preview")
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
    
    # Proxy Configuration
    HTTP_PROXY = os.getenv("HTTP_PROXY")
    HTTPS_PROXY = os.getenv("HTTPS_PROXY")
    
    # Experiment Configuration
    TOP_K_RETRIEVAL = int(os.getenv("TOP_K_RETRIEVAL", "100"))
    TOP_K_RERANK = int(os.getenv("TOP_K_RERANK", "10"))
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "32"))
    
    # Data paths
    DATA_DIR = "data"
    INDEX_DIR = "indices"
    RESULTS_DIR = "results"
    
    @classmethod
    def get_proxy_config(cls):
        """Get proxy configuration for requests"""
        if cls.HTTP_PROXY or cls.HTTPS_PROXY:
            return {
                "http": cls.HTTP_PROXY,
                "https": cls.HTTPS_PROXY
            }
        return None
    
    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        if not cls.OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY is required")
        
        # Create necessary directories
        for dir_path in [cls.DATA_DIR, cls.INDEX_DIR, cls.RESULTS_DIR]:
            os.makedirs(dir_path, exist_ok=True)
