#!/usr/bin/env python3
"""
改进的评估框架

解决评估偏差问题，提供多种公平的评估方法：
1. 官方 qrels 评估
2. 人工评估框架
3. 多维度评估
4. 交叉验证评估
"""

import json
import os
import random
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from src.llm4ranking.datasets import DL19Dataset
from src.llm4ranking.evaluation.metrics import RankingEvaluator


@dataclass
class EvaluationResult:
    """评估结果数据类"""
    method_name: str
    metrics: Dict[str, float]
    qrels_type: str
    num_queries: int
    description: str = ""


class ImprovedEvaluator:
    """改进的评估器"""
    
    def __init__(self):
        self.dataset = DL19Dataset()
        self.base_evaluator = RankingEvaluator()
        self.results_cache = {}
    
    def load_data_and_qrels(self, use_official_qrels: bool = True):
        """加载数据和 qrels"""
        print("🔄 Loading data and qrels...")
        
        # 加载基础数据
        self.dataset.load_from_toy_data()
        
        # 加载不同类型的 qrels
        self.qrels = {}
        
        if use_official_qrels:
            # 尝试加载官方 qrels
            self.qrels['official'] = self.dataset.load_official_qrels()
        
        # 创建其他类型的 qrels 作为对比
        self.qrels['bm25_based'] = self.dataset._create_dummy_qrels()
        self.qrels['random'] = self._create_random_qrels()
        
        print(f"✅ Loaded {len(self.qrels)} types of qrels")
    
    def _create_random_qrels(self) -> Dict[str, Dict[str, int]]:
        """创建随机 qrels（控制实验）"""
        qrels = {}
        
        with open("toy_data/dl19_bm25_top20.jsonl", 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line)
                query_id = str(item['hits'][0]['qid'])
                
                qrels[query_id] = {}
                doc_ids = [hit['docid'] for hit in item['hits']]
                
                # 随机分配相关性分数
                random.shuffle(doc_ids)
                for i, doc_id in enumerate(doc_ids):
                    if i < 5:
                        qrels[query_id][doc_id] = 3
                    elif i < 10:
                        qrels[query_id][doc_id] = 2
                    elif i < 15:
                        qrels[query_id][doc_id] = 1
                    else:
                        qrels[query_id][doc_id] = 0
        
        return qrels
    
    def evaluate_with_multiple_qrels(
        self, 
        bm25_results: Dict[str, List[str]], 
        llm_results: Dict[str, List[str]]
    ) -> List[EvaluationResult]:
        """使用多种 qrels 进行评估"""
        
        evaluation_results = []
        
        for qrels_type, qrels in self.qrels.items():
            print(f"\n📊 Evaluating with {qrels_type} qrels...")
            
            # 过滤只包含在 qrels 中的查询
            common_queries = set(bm25_results.keys()) & set(llm_results.keys()) & set(qrels.keys())
            
            if not common_queries:
                print(f"⚠️  No common queries found for {qrels_type} qrels")
                continue
            
            filtered_bm25 = {q: bm25_results[q] for q in common_queries}
            filtered_llm = {q: llm_results[q] for q in common_queries}
            filtered_qrels = {q: qrels[q] for q in common_queries}
            
            # 评估 BM25
            bm25_metrics = self.base_evaluator.evaluate_run(
                filtered_bm25, filtered_qrels, [1, 5, 10, 20]
            )
            
            # 评估 LLM
            llm_metrics = self.base_evaluator.evaluate_run(
                filtered_llm, filtered_qrels, [1, 5, 10, 20]
            )
            
            # 保存结果
            evaluation_results.extend([
                EvaluationResult(
                    method_name="BM25",
                    metrics=bm25_metrics,
                    qrels_type=qrels_type,
                    num_queries=len(common_queries),
                    description=f"BM25 baseline evaluated with {qrels_type} qrels"
                ),
                EvaluationResult(
                    method_name="LLM",
                    metrics=llm_metrics,
                    qrels_type=qrels_type,
                    num_queries=len(common_queries),
                    description=f"LLM reranking evaluated with {qrels_type} qrels"
                )
            ])
            
            print(f"✅ Evaluated {len(common_queries)} queries with {qrels_type} qrels")
        
        return evaluation_results
    
    def cross_validation_evaluation(
        self,
        bm25_results: Dict[str, List[str]], 
        llm_results: Dict[str, List[str]],
        n_folds: int = 5
    ) -> Dict[str, List[float]]:
        """交叉验证评估"""
        print(f"\n🔄 Running {n_folds}-fold cross validation...")
        
        # 获取所有查询
        all_queries = list(set(bm25_results.keys()) & set(llm_results.keys()))
        random.shuffle(all_queries)
        
        fold_size = len(all_queries) // n_folds
        cv_results = {'BM25': [], 'LLM': []}
        
        for fold in range(n_folds):
            print(f"  Fold {fold + 1}/{n_folds}")
            
            # 分割数据
            start_idx = fold * fold_size
            end_idx = start_idx + fold_size if fold < n_folds - 1 else len(all_queries)
            
            test_queries = all_queries[start_idx:end_idx]
            train_queries = [q for q in all_queries if q not in test_queries]
            
            # 使用训练集创建 qrels（基于 BM25）
            fold_qrels = {}
            for query_id in train_queries + test_queries:
                if query_id in self.qrels['bm25_based']:
                    fold_qrels[query_id] = self.qrels['bm25_based'][query_id]
            
            # 在测试集上评估
            test_bm25 = {q: bm25_results[q] for q in test_queries if q in bm25_results}
            test_llm = {q: llm_results[q] for q in test_queries if q in llm_results}
            
            if test_bm25 and test_llm:
                bm25_metrics = self.base_evaluator.evaluate_run(test_bm25, fold_qrels, [10])
                llm_metrics = self.base_evaluator.evaluate_run(test_llm, fold_qrels, [10])
                
                cv_results['BM25'].append(bm25_metrics.get('NDCG@10', 0))
                cv_results['LLM'].append(llm_metrics.get('NDCG@10', 0))
        
        return cv_results
    
    def semantic_similarity_evaluation(
        self,
        queries: Dict[str, str],
        bm25_results: Dict[str, List[str]], 
        llm_results: Dict[str, List[str]]
    ) -> Dict[str, float]:
        """基于语义相似度的评估（简化版）"""
        print("\n🧠 Running semantic similarity evaluation...")
        
        # 这里是一个简化的实现
        # 在实际应用中，可以使用 sentence transformers 等工具
        
        semantic_scores = {'BM25': [], 'LLM': []}
        
        for query_id in queries:
            if query_id not in bm25_results or query_id not in llm_results:
                continue
            
            query_text = queries[query_id].lower()
            
            # 简单的语义匹配评分（基于关键词重叠）
            def simple_semantic_score(query: str, doc_texts: List[str], top_k: int = 3) -> float:
                query_words = set(query.split())
                scores = []
                
                for doc_text in doc_texts[:top_k]:
                    doc_words = set(doc_text.lower().split())
                    overlap = len(query_words & doc_words)
                    score = overlap / len(query_words) if query_words else 0
                    scores.append(score)
                
                return np.mean(scores) if scores else 0
            
            # 获取文档文本（这里简化处理）
            bm25_docs = bm25_results[query_id][:3]
            llm_docs = llm_results[query_id][:3]
            
            # 计算语义分数
            bm25_score = simple_semantic_score(query_text, bm25_docs)
            llm_score = simple_semantic_score(query_text, llm_docs)
            
            semantic_scores['BM25'].append(bm25_score)
            semantic_scores['LLM'].append(llm_score)
        
        return {
            'BM25_avg': np.mean(semantic_scores['BM25']) if semantic_scores['BM25'] else 0,
            'LLM_avg': np.mean(semantic_scores['LLM']) if semantic_scores['LLM'] else 0,
            'improvement': (np.mean(semantic_scores['LLM']) - np.mean(semantic_scores['BM25'])) if semantic_scores['BM25'] and semantic_scores['LLM'] else 0
        }
    
    def print_comprehensive_results(self, evaluation_results: List[EvaluationResult]):
        """打印综合评估结果"""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE EVALUATION RESULTS")
        print("=" * 80)
        
        # 按 qrels 类型分组
        by_qrels = {}
        for result in evaluation_results:
            if result.qrels_type not in by_qrels:
                by_qrels[result.qrels_type] = {}
            by_qrels[result.qrels_type][result.method_name] = result
        
        for qrels_type, methods in by_qrels.items():
            print(f"\n🔍 Results with {qrels_type.upper()} qrels:")
            print("-" * 60)
            
            if 'BM25' in methods and 'LLM' in methods:
                bm25_result = methods['BM25']
                llm_result = methods['LLM']
                
                print(f"{'Metric':<12} {'BM25':<10} {'LLM':<10} {'Improvement':<12}")
                print("-" * 50)
                
                for metric in ['NDCG@1', 'NDCG@5', 'NDCG@10', 'MAP']:
                    bm25_val = bm25_result.metrics.get(metric, 0)
                    llm_val = llm_result.metrics.get(metric, 0)
                    improvement = ((llm_val - bm25_val) / bm25_val * 100) if bm25_val > 0 else 0
                    
                    print(f"{metric:<12} {bm25_val:<10.3f} {llm_val:<10.3f} {improvement:<12.2f}%")
                
                print(f"Queries: {bm25_result.num_queries}")
        
        # 分析结果
        self._analyze_comprehensive_results(by_qrels)
    
    def _analyze_comprehensive_results(self, by_qrels: Dict):
        """分析综合评估结果"""
        print("\n" + "=" * 80)
        print("🎯 ANALYSIS AND INSIGHTS")
        print("=" * 80)
        
        insights = []
        
        # 检查不同 qrels 下的表现
        if 'official' in by_qrels and 'bm25_based' in by_qrels:
            insights.append("✅ Comparison with official vs BM25-based qrels available")
        elif 'bm25_based' in by_qrels and 'random' in by_qrels:
            insights.append("📊 Comparison with BM25-based vs random qrels shows evaluation bias")
        
        # 检查 LLM 在不同评估下的表现
        llm_improvements = []
        for qrels_type, methods in by_qrels.items():
            if 'BM25' in methods and 'LLM' in methods:
                bm25_ndcg5 = methods['BM25'].metrics.get('NDCG@5', 0)
                llm_ndcg5 = methods['LLM'].metrics.get('NDCG@5', 0)
                if bm25_ndcg5 > 0:
                    improvement = (llm_ndcg5 - bm25_ndcg5) / bm25_ndcg5 * 100
                    llm_improvements.append((qrels_type, improvement))
        
        if llm_improvements:
            print("\n📈 LLM Performance across different qrels:")
            for qrels_type, improvement in llm_improvements:
                status = "📈" if improvement > 0 else "📉" if improvement < -5 else "➡️"
                print(f"  {status} {qrels_type}: {improvement:+.1f}%")
        
        print("\n💡 Key Insights:")
        for insight in insights:
            print(f"  {insight}")
        
        print("\n🔧 Recommendations:")
        print("  1. Use official qrels when available for fair evaluation")
        print("  2. Consider multiple evaluation perspectives")
        print("  3. Analyze specific cases for semantic understanding")
        print("  4. Conduct user studies for real-world validation")


def main():
    """主函数 - 运行改进的评估"""
    
    # 检查是否有演示结果
    if not os.path.exists("results/bm25_results.json"):
        print("❌ No demo results found. Please run the demo first:")
        print("   python run_demo.py --queries 5")
        return
    
    # 加载演示结果
    with open("results/bm25_results.json", 'r', encoding='utf-8') as f:
        bm25_data = json.load(f)
    
    with open("results/reranked_results.json", 'r', encoding='utf-8') as f:
        llm_data = json.load(f)
    
    # 转换为排序格式
    bm25_results = {qid: [doc['doc_id'] for doc in docs] for qid, docs in bm25_data.items()}
    llm_results = {qid: [doc['doc_id'] for doc in docs] for qid, docs in llm_data.items()}
    
    # 创建改进的评估器
    evaluator = ImprovedEvaluator()
    
    print("🚀 Running Improved Evaluation Framework")
    print("=" * 60)
    
    # 加载数据和 qrels
    evaluator.load_data_and_qrels(use_official_qrels=True)
    
    # 多 qrels 评估
    evaluation_results = evaluator.evaluate_with_multiple_qrels(bm25_results, llm_results)
    
    # 打印结果
    evaluator.print_comprehensive_results(evaluation_results)
    
    # 交叉验证（如果有足够的查询）
    if len(bm25_results) >= 5:
        cv_results = evaluator.cross_validation_evaluation(bm25_results, llm_results)
        
        print(f"\n🔄 Cross-Validation Results (NDCG@10):")
        print(f"  BM25: {np.mean(cv_results['BM25']):.3f} ± {np.std(cv_results['BM25']):.3f}")
        print(f"  LLM:  {np.mean(cv_results['LLM']):.3f} ± {np.std(cv_results['LLM']):.3f}")
    
    print("\n✅ Improved evaluation completed!")
    print("📖 This framework addresses evaluation bias and provides fairer comparison")


if __name__ == "__main__":
    main()
