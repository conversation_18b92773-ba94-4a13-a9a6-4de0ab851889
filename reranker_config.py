#!/usr/bin/env python3
"""
LLM4Ranking 配置管理

提供预定义的配置和最佳实践参数
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class RerankerConfig:
    """重排器配置类"""
    approach: str
    model_name: str
    model_type: str = "openrouter"
    reranking_args: Dict[str, Any] = None
    model_fw_args: Dict[str, Any] = None
    description: str = ""


class ConfigManager:
    """配置管理器"""
    
    # 预定义配置
    CONFIGS = {
        # RankGPT 配置
        "rankgpt_fast": RerankerConfig(
            approach="rankgpt",
            model_name="openai/gpt-3.5-turbo",
            reranking_args={"window_size": 10, "step": 5, "truncate_length": 200},
            model_fw_args={"max_new_tokens": 80, "do_sample": False},
            description="快速 RankGPT 配置，适合大量文档"
        ),
        
        "rankgpt_balanced": RerankerConfig(
            approach="rankgpt",
            model_name="openai/gpt-4-1106-preview",
            reranking_args={"window_size": 20, "step": 10, "truncate_length": 300},
            model_fw_args={"max_new_tokens": 120, "do_sample": False},
            description="平衡的 RankGPT 配置，质量和速度兼顾"
        ),
        
        "rankgpt_quality": RerankerConfig(
            approach="rankgpt",
            model_name="openai/gpt-4",
            reranking_args={"window_size": 20, "step": 5, "truncate_length": 500},
            model_fw_args={"max_new_tokens": 150, "do_sample": False},
            description="高质量 RankGPT 配置，追求最佳效果"
        ),
        
        # RelevanceGeneration 配置
        "rel_gen_fast": RerankerConfig(
            approach="rel-gen",
            model_name="openai/gpt-3.5-turbo",
            reranking_args={"truncate_length": 200},
            model_fw_args={"max_new_tokens": 50, "do_sample": False},
            description="快速相关性生成，适合实时应用"
        ),
        
        "rel_gen_quality": RerankerConfig(
            approach="rel-gen",
            model_name="openai/gpt-4-1106-preview",
            reranking_args={"truncate_length": 400},
            model_fw_args={"max_new_tokens": 100, "do_sample": False},
            description="高质量相关性生成"
        ),
        
        # PRP 配置
        "prp_heap": RerankerConfig(
            approach="prp-heap",
            model_name="openai/gpt-4-1106-preview",
            reranking_args={"topk": 10, "truncate_length": 300},
            model_fw_args={"max_new_tokens": 80, "do_sample": False},
            description="PRP 堆排序，适合精确排序"
        ),
        
        # Claude 配置
        "claude_rankgpt": RerankerConfig(
            approach="rankgpt",
            model_name="anthropic/claude-3-sonnet",
            reranking_args={"window_size": 20, "step": 10, "truncate_length": 400},
            model_fw_args={"max_new_tokens": 120, "do_sample": False},
            description="使用 Claude 的 RankGPT 配置"
        ),
        
        # 经济型配置
        "budget_friendly": RerankerConfig(
            approach="rel-gen",
            model_name="openai/gpt-3.5-turbo",
            reranking_args={"truncate_length": 150},
            model_fw_args={"max_new_tokens": 30, "do_sample": False},
            description="经济型配置，降低 API 成本"
        ),
    }
    
    @classmethod
    def get_config(cls, config_name: str) -> RerankerConfig:
        """获取预定义配置"""
        if config_name not in cls.CONFIGS:
            available = ", ".join(cls.CONFIGS.keys())
            raise ValueError(f"Unknown config '{config_name}'. Available: {available}")
        
        return cls.CONFIGS[config_name]
    
    @classmethod
    def list_configs(cls) -> Dict[str, str]:
        """列出所有可用配置"""
        return {name: config.description for name, config in cls.CONFIGS.items()}
    
    @classmethod
    def create_model_args(
        cls,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        proxy_config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """创建模型参数"""
        # 获取 API 密钥
        if api_key is None:
            api_key = os.getenv("OPENROUTER_API_KEY") or os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("API key is required")
        
        # 设置基础 URL
        if base_url is None:
            base_url = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
        
        # 设置代理
        if proxy_config is None and (os.getenv("HTTP_PROXY") or os.getenv("HTTPS_PROXY")):
            proxy_config = {
                "http": os.getenv("HTTP_PROXY"),
                "https": os.getenv("HTTPS_PROXY")
            }
        
        model_args = {
            "api_key": api_key,
            "base_url": base_url,
        }
        
        if proxy_config:
            model_args["proxy_config"] = proxy_config
        
        return model_args


def create_reranker_from_config(
    config_name: str,
    model_name: Optional[str] = None,
    **kwargs
):
    """从配置名称创建重排器"""
    from llm4ranking import Reranker
    
    # 获取配置
    config = ConfigManager.get_config(config_name)
    
    # 允许覆盖模型名称
    if model_name:
        config.model_name = model_name
    
    # 创建模型参数
    model_args = ConfigManager.create_model_args(**kwargs)
    model_args["model"] = config.model_name
    
    # 创建重排器
    return Reranker(
        reranking_approach=config.approach,
        model_type=config.model_type,
        model_name=config.model_name,
        model_args=model_args,
        reranking_args=config.reranking_args or {},
        model_fw_args=config.model_fw_args or {}
    )


# ============================================================================
# 使用示例
# ============================================================================

def demo_configs():
    """演示不同配置的使用"""
    
    print("📋 可用配置:")
    print("-" * 50)
    
    configs = ConfigManager.list_configs()
    for name, description in configs.items():
        print(f"  {name:<20}: {description}")
    
    print("\n🚀 配置使用示例:")
    print("-" * 50)
    
    # 示例查询和文档
    query = "机器学习算法"
    documents = [
        "机器学习是人工智能的一个重要分支",
        "深度学习使用多层神经网络",
        "监督学习需要标记的训练数据",
        "无监督学习发现数据中的隐藏模式"
    ]
    
    # 测试不同配置
    test_configs = ["rankgpt_fast", "rel_gen_fast", "budget_friendly"]
    
    for config_name in test_configs:
        print(f"\n🧪 测试配置: {config_name}")
        try:
            config = ConfigManager.get_config(config_name)
            print(f"   方法: {config.approach}")
            print(f"   模型: {config.model_name}")
            print(f"   描述: {config.description}")
            
            # 这里只是展示配置，不实际调用 API
            print("   ✅ 配置有效")
            
        except Exception as e:
            print(f"   ❌ 配置错误: {e}")


if __name__ == "__main__":
    demo_configs()
    
    print("\n💡 使用方法:")
    print("```python")
    print("from reranker_config import create_reranker_from_config")
    print("")
    print("# 使用预定义配置")
    print("reranker = create_reranker_from_config('rankgpt_balanced')")
    print("")
    print("# 覆盖模型名称")
    print("reranker = create_reranker_from_config(")
    print("    'rankgpt_fast',")
    print("    model_name='anthropic/claude-3-haiku'")
    print(")")
    print("")
    print("# 执行重排")
    print("reranked_docs, indices = reranker.rerank(")
    print("    query='your query',")
    print("    candidates=['doc1', 'doc2', 'doc3'],")
    print("    return_indices=True")
    print(")")
    print("```")
