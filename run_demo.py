#!/usr/bin/env python3
"""
简化的 DEMO 运行脚本

这个脚本提供了一个简单的命令行界面来运行 DL19 BM25 + LLM 重排演示
"""

import argparse
import os
import sys
from demo_dl19_bm25_llm_rerank import DL19Demo


def check_environment():
    """检查环境配置"""
    print("Checking environment...")
    
    # 检查 .env 文件
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please copy .env.example to .env and fill in your OpenRouter API key")
        return False
    
    # 检查 API key
    from config import Config
    if not Config.OPENROUTER_API_KEY:
        print("❌ OPENROUTER_API_KEY not found in .env file!")
        print("Please add your OpenRouter API key to the .env file")
        return False
    
    # 检查 toy_data 文件
    if not os.path.exists('toy_data/dl19_bm25_top20.jsonl'):
        print("❌ toy_data/dl19_bm25_top20.jsonl not found!")
        print("Please make sure the toy data file exists")
        return False
    
    print("✅ Environment check passed!")
    return True


def main():
    parser = argparse.ArgumentParser(description='Run DL19 BM25 + LLM Reranking Demo')
    parser.add_argument('--queries', type=int, default=3, 
                       help='Number of queries to process (default: 3)')
    parser.add_argument('--no-save', action='store_true', 
                       help='Do not save results to files')
    parser.add_argument('--model', type=str, 
                       help='Override model name (e.g., openai/gpt-4-1106-preview)')
    
    args = parser.parse_args()
    
    # 检查环境
    if not check_environment():
        return 1
    
    # 设置模型（如果指定）
    if args.model:
        os.environ['DEFAULT_MODEL'] = args.model
        print(f"Using model: {args.model}")
    
    try:
        print("\n🚀 Starting DL19 BM25 + LLM Reranking Demo...")
        print(f"Processing {args.queries} queries")
        print(f"Save results: {not args.no_save}")
        
        # 运行演示
        demo = DL19Demo()
        demo.run_demo(
            limit_queries=args.queries,
            save_results=not args.no_save
        )
        
        print("\n✅ Demo completed successfully!")
        
        if not args.no_save:
            print("\n📁 Results saved to:")
            print("  - results/bm25_results.json")
            print("  - results/reranked_results.json") 
            print("  - results/evaluation_results.json")
        
    except KeyboardInterrupt:
        print("\n❌ Demo interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error running demo: {e}")
        print("\nTroubleshooting:")
        print("1. Check your .env file configuration")
        print("2. Verify your OpenRouter API key is valid")
        print("3. Check your internet connection and proxy settings")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
