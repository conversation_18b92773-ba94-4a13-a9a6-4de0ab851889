# PyTerrier DL19 BM25 演示

这个项目展示了如何使用 PyTerrier 框架在 TREC Deep Learning 2019 (DL19) 数据集上进行 BM25 检索。

## 🎯 项目概述

PyTerrier 是一个基于 Java Terrier IR 平台的 Python 框架，专为信息检索研究设计。本演示包括：

- 📚 **文档索引**：使用 PyTerrier 构建倒排索引
- 🔍 **BM25 检索**：实现经典的 BM25 排序算法
- 📊 **结果评估**：使用标准 IR 评估指标
- 🎨 **可视化展示**：清晰的结果展示和分析

## 📁 文件结构

```
├── pyterrier_dl19_demo.py      # 完整的 PyTerrier 演示
├── pyterrier_simple_demo.py    # 简化版演示（适合快速测试）
├── setup_pyterrier.py          # 环境设置脚本
├── PYTERRIER_README.md         # 本文档
└── pyterrier_data/             # 数据存储目录（自动创建）
    ├── documents.jsonl         # 文档集合
    ├── queries.tsv            # 查询集合
    ├── qrels.txt              # 相关性判断
    └── dl19_index/            # PyTerrier 索引
```

## 🚀 快速开始

### 方法 1：简化演示（推荐新手）

```bash
# 直接运行简化演示
python pyterrier_simple_demo.py
```

这个版本会：
- ✅ 自动检查 PyTerrier 是否可用
- ✅ 如果不可用，展示概念性演示
- ✅ 使用内置的示例数据
- ✅ 提供清晰的安装指导

### 方法 2：完整演示

```bash
# 1. 设置环境
python setup_pyterrier.py

# 2. 运行完整演示
python pyterrier_dl19_demo.py
```

## 🔧 环境要求

### 必需依赖

1. **Python 3.7+**
2. **Java 8+** （PyTerrier 的核心依赖）
3. **Python 包**：
   - `python-terrier`
   - `pandas`
   - `numpy`
   - `requests`

### 安装步骤

#### 1. 安装 Java

**macOS:**
```bash
# 使用 Homebrew
brew install openjdk@11

# 或下载 Oracle JDK
# https://www.oracle.com/java/technologies/downloads/
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install openjdk-11-jdk
```

**Windows:**
```bash
# 使用 Chocolatey
choco install openjdk11

# 或下载 Oracle JDK
# https://www.oracle.com/java/technologies/downloads/
```

#### 2. 安装 PyTerrier

```bash
# 自动安装（推荐）
python setup_pyterrier.py

# 或手动安装
pip install python-terrier pandas numpy requests
```

#### 3. 验证安装

```bash
# 运行测试脚本
python test_pyterrier.py

# 或运行简化演示
python pyterrier_simple_demo.py
```

## 📊 演示功能

### 核心功能

1. **文档索引**
   ```python
   # 使用 PyTerrier 构建索引
   indexer = pt.IterDictIndexer(index_path)
   index_ref = indexer.index(documents)
   ```

2. **BM25 检索**
   ```python
   # 创建 BM25 检索器
   bm25 = pt.BatchRetrieve(index, wmodel="BM25")
   results = bm25.transform(queries)
   ```

3. **结果评估**
   ```python
   # 使用内置评估指标
   evaluation = pt.Evaluate(results, qrels, 
                           metrics=['map', 'ndcg_cut_10', 'P_10'])
   ```

### 示例输出

```
🚀 PyTerrier DL19 BM25 Demo
==================================================
✅ PyTerrier initialized
📖 Loaded 10 queries
🔨 Building index...
✅ Index built successfully
📊 Index statistics:
   Documents: 10
   Terms: 156
   Tokens: 892
🔍 Running BM25 retrieval for 10 queries...
✅ Retrieved 100 results
📊 Average results per query: 10.0

🔍 Sample Retrieval Results:
============================================================

📝 Query 1: who is robert gray
----------------------------------------
   1. [doc1] Score: 2.8934
   2. [doc4] Score: 0.4521
   3. [doc2] Score: 0.2156

📈 Evaluation Results:
==================================================
map             : 0.8500
ndcg_cut_10     : 0.9234
P_10            : 0.9000
recall_10       : 0.7500
```

## 🎓 学习价值

### PyTerrier 特性

1. **易用性**：
   - 简洁的 Python API
   - 与 Pandas 无缝集成
   - 自动化的索引和检索流程

2. **功能丰富**：
   - 多种检索模型（BM25, TF-IDF, DPH 等）
   - 内置评估指标
   - 支持查询扩展和重排

3. **可扩展性**：
   - 支持大规模文档集合
   - 可与深度学习模型集成
   - 支持分布式检索

### 与传统方法对比

| 特性 | PyTerrier | 传统实现 |
|------|-----------|----------|
| 索引构建 | 自动化 | 手动实现 |
| 检索算法 | 内置多种 | 需要编程 |
| 评估指标 | 内置标准指标 | 手动计算 |
| 数据处理 | Pandas 集成 | 自定义格式 |
| 可重现性 | 高 | 依赖实现 |

## 🔍 高级用法

### 自定义检索模型

```python
# 使用不同的检索模型
tf_idf = pt.BatchRetrieve(index, wmodel="TF_IDF")
dph = pt.BatchRetrieve(index, wmodel="DPH")

# 组合多个检索器
combined = (bm25 % 100) >> pt.text.get_text(index, "text") >> neural_reranker
```

### 查询扩展

```python
# 使用 RM3 查询扩展
rm3 = pt.rewrite.RM3(index)
expanded_retrieval = bm25 >> rm3 >> bm25
```

### 评估和分析

```python
# 详细评估
from pyterrier.measures import *

evaluation = pt.Experiment(
    [bm25, tf_idf, dph],
    topics, qrels,
    eval_metrics=[MAP, nDCG@10, P@10, R@100]
)
```

## 🐛 常见问题

### 1. Java 相关问题

**问题**：`Java not found` 或 `JAVA_HOME not set`

**解决**：
```bash
# 检查 Java 安装
java -version

# 设置 JAVA_HOME（如果需要）
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
```

### 2. 内存问题

**问题**：`OutOfMemoryError` 或索引构建失败

**解决**：
```python
# 增加 Java 堆内存
import pyterrier as pt
pt.init(mem=8000)  # 8GB 内存
```

### 3. 权限问题

**问题**：无法创建索引目录

**解决**：
```bash
# 确保有写权限
chmod 755 ./pyterrier_data
```

## 📚 参考资源

### 官方文档
- [PyTerrier 官方文档](https://pyterrier.readthedocs.io/)
- [PyTerrier GitHub](https://github.com/terrier-org/pyterrier)
- [Terrier IR 平台](http://terrier.org/)

### 学术论文
- [PyTerrier: Declarative Experimentation in Python from BM25 to Dense Retrieval](https://arxiv.org/abs/2007.14271)

### 教程和示例
- [PyTerrier Tutorials](https://github.com/terrier-org/ecir2021tutorial)
- [TREC Deep Learning Track](https://microsoft.github.io/msmarco/TREC-Deep-Learning)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 改进建议
- 添加更多检索模型演示
- 集成神经网络重排模型
- 支持更多数据集
- 改进可视化功能

## 📄 许可证

本项目采用 MIT 许可证。

---

**Happy Retrieving with PyTerrier! 🎉**
