#!/usr/bin/env python3
"""
DEMO: BM25 检索 + 大语言模型重排在 DL 2019 数据集上的性能测试

这个演示脚本展示了如何：
1. 使用 BM25 进行初始检索（使用现有的 top-20 结果）
2. 使用大语言模型对前 20 个结果进行重排
3. 评估重排前后的性能差异
4. 可视化结果

使用方法:
1. 复制 .env.example 到 .env 并填入你的 OpenRouter API key
2. 运行: python demo_dl19_bm25_llm_rerank.py
"""

import os
import json
import time
from typing import Dict, List, Tuple
from tqdm import tqdm

# 导入项目模块
from config import Config
from src.llm4ranking import Reranker
from src.llm4ranking.datasets import DL19Dataset
from src.llm4ranking.evaluation.metrics import RankingEvaluator, print_results_table


class DL19Demo:
    """DL19 数据集上的 BM25 + LLM 重排演示"""
    
    def __init__(self):
        """初始化演示"""
        # 验证配置
        Config.validate_config()
        
        # 初始化组件
        self.dataset = DL19Dataset()
        self.evaluator = RankingEvaluator()
        
        # 加载数据
        print("Loading DL19 dataset...")
        self.dataset.load_from_toy_data()
        
        # 获取数据
        self.queries = self.dataset.get_queries()
        self.documents = self.dataset.get_documents()
        
        print(f"Loaded {len(self.queries)} queries and {len(self.documents)} documents")
        
        # 初始化重排器
        self.reranker = None
        self._init_reranker()
    
    def _init_reranker(self):
        """初始化 LLM 重排器"""
        print("Initializing LLM reranker...")
        
        # 获取代理配置
        proxy_config = Config.get_proxy_config()
        
        # 创建模型参数
        model_args = {
            "model": Config.DEFAULT_MODEL,
            "api_key": Config.OPENROUTER_API_KEY,
            "base_url": Config.OPENROUTER_BASE_URL,
        }
        
        if proxy_config:
            model_args["proxy_config"] = proxy_config
            print(f"Using proxy: {proxy_config}")
        
        # 初始化重排器
        # 为 OpenAI/OpenRouter API 使用正确的参数
        model_fw_args = {
            "max_completion_tokens": 120,
            # 移除 do_sample 参数，因为 OpenAI API 不支持
        }

        # 重排参数，覆盖默认的 do_sample 参数
        reranking_args = {
            "window_size": 20,
            "step": 10,
            "truncate_length": 300,
            # 显式移除 do_sample 参数，因为 OpenAI API 不支持
            "do_sample": None,  # 这会被过滤掉
        }

        self.reranker = Reranker(
            reranking_approach="rankgpt",
            model_type="openrouter",
            model_name=Config.DEFAULT_MODEL,
            model_args=model_args,
            reranking_args=reranking_args,
            model_fw_args=model_fw_args
        )
        
        print(f"Reranker initialized with model: {Config.DEFAULT_MODEL}")
    
    def load_bm25_results(self) -> Dict[str, List[Tuple[str, str, float]]]:
        """
        从 toy_data 加载 BM25 结果
        
        Returns:
            Dict mapping query_id to list of (doc_id, doc_text, score) tuples
        """
        print("Loading BM25 baseline results...")
        
        bm25_results = {}
        
        with open("toy_data/dl19_bm25_top20.jsonl", 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line)
                query = item['query']
                
                # 找到对应的 query_id
                query_id = None
                for qid, qtext in self.queries.items():
                    if qtext == query:
                        query_id = qid
                        break
                
                if query_id is None:
                    # 如果没找到，使用第一个 hit 的 qid
                    query_id = str(item['hits'][0]['qid'])
                
                # 提取文档信息
                doc_results = []
                for hit in item['hits']:
                    doc_id = hit['docid']
                    doc_text = hit['content']
                    score = hit['score']
                    doc_results.append((doc_id, doc_text, score))
                
                bm25_results[query_id] = doc_results
        
        print(f"Loaded BM25 results for {len(bm25_results)} queries")
        return bm25_results
    
    def perform_llm_reranking(self, bm25_results: Dict[str, List[Tuple[str, str, float]]], 
                            limit_queries: int = None) -> Dict[str, List[Tuple[str, str, float]]]:
        """
        使用 LLM 对 BM25 结果进行重排
        
        Args:
            bm25_results: BM25 结果
            limit_queries: 限制处理的查询数量（用于快速测试）
            
        Returns:
            重排后的结果
        """
        print("Performing LLM reranking...")
        
        reranked_results = {}
        
        # 限制查询数量（如果指定）
        queries_to_process = list(bm25_results.keys())
        if limit_queries:
            queries_to_process = queries_to_process[:limit_queries]
            print(f"Processing only first {limit_queries} queries for demo")
        
        for query_id in tqdm(queries_to_process, desc="Reranking queries"):
            query_text = self.queries[query_id]
            doc_results = bm25_results[query_id]
            
            # 提取文档文本
            doc_texts = [doc_text for _, doc_text, _ in doc_results]
            doc_ids = [doc_id for doc_id, _, _ in doc_results]

            try:
                # 执行重排
                _, reranked_indices = self.reranker.rerank(
                    query=query_text,
                    candidates=doc_texts,
                    return_indices=True
                )
                
                # 重新组织结果
                reranked_doc_results = []
                for i, idx in enumerate(reranked_indices):
                    doc_id = doc_ids[idx]
                    doc_text = doc_texts[idx]
                    # 使用重排后的位置作为新的分数（位置越靠前分数越高）
                    new_score = len(reranked_indices) - i
                    reranked_doc_results.append((doc_id, doc_text, new_score))
                
                reranked_results[query_id] = reranked_doc_results
                
            except Exception as e:
                print(f"Error reranking query {query_id}: {e}")
                # 如果重排失败，使用原始结果
                reranked_results[query_id] = doc_results
        
        print(f"Completed reranking for {len(reranked_results)} queries")
        return reranked_results
    
    def create_dummy_qrels(self) -> Dict[str, Dict[str, int]]:
        """
        创建虚拟的相关性判断（用于演示）
        在实际应用中，这些应该来自人工标注
        """
        print("Creating dummy relevance judgments for demo...")
        
        qrels = {}
        
        # 为每个查询创建虚拟的相关性判断
        # 这里我们假设 BM25 分数较高的文档更相关
        with open("toy_data/dl19_bm25_top20.jsonl", 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line)
                query_id = str(item['hits'][0]['qid'])
                
                qrels[query_id] = {}
                
                # 根据 BM25 分数创建相关性判断
                # 前5个文档标记为高度相关(3)，6-10个为相关(2)，11-15个为部分相关(1)，其余为不相关(0)
                for i, hit in enumerate(item['hits']):
                    doc_id = hit['docid']
                    if i < 5:
                        qrels[query_id][doc_id] = 3
                    elif i < 10:
                        qrels[query_id][doc_id] = 2
                    elif i < 15:
                        qrels[query_id][doc_id] = 1
                    else:
                        qrels[query_id][doc_id] = 0
        
        print(f"Created dummy qrels for {len(qrels)} queries")
        return qrels

    def evaluate_results(self, bm25_results: Dict[str, List[Tuple[str, str, float]]],
                        reranked_results: Dict[str, List[Tuple[str, str, float]]],
                        qrels: Dict[str, Dict[str, int]]) -> Dict[str, Dict[str, float]]:
        """
        评估 BM25 和重排结果

        Args:
            bm25_results: BM25 结果
            reranked_results: 重排结果
            qrels: 相关性判断

        Returns:
            评估结果
        """
        print("Evaluating results...")

        # 转换结果格式为评估器需要的格式
        def convert_results(results):
            converted = {}
            for query_id, doc_results in results.items():
                converted[query_id] = [doc_id for doc_id, _, _ in doc_results]
            return converted

        bm25_converted = convert_results(bm25_results)
        reranked_converted = convert_results(reranked_results)

        # 执行评估
        comparison_results = self.evaluator.compare_runs(
            baseline_results=bm25_converted,
            reranked_results=reranked_converted,
            qrels=qrels,
            k_values=[1, 5, 10, 20]
        )

        return comparison_results

    def save_results(self, bm25_results: Dict, reranked_results: Dict,
                    evaluation_results: Dict, output_dir: str = "results"):
        """保存结果到文件"""
        os.makedirs(output_dir, exist_ok=True)

        # 保存原始结果
        with open(f"{output_dir}/bm25_results.json", 'w', encoding='utf-8') as f:
            # 转换为可序列化的格式
            serializable_bm25 = {}
            for qid, docs in bm25_results.items():
                serializable_bm25[qid] = [
                    {"doc_id": doc_id, "doc_text": doc_text[:200] + "...", "score": score}
                    for doc_id, doc_text, score in docs
                ]
            json.dump(serializable_bm25, f, indent=2, ensure_ascii=False)

        with open(f"{output_dir}/reranked_results.json", 'w', encoding='utf-8') as f:
            serializable_reranked = {}
            for qid, docs in reranked_results.items():
                serializable_reranked[qid] = [
                    {"doc_id": doc_id, "doc_text": doc_text[:200] + "...", "score": score}
                    for doc_id, doc_text, score in docs
                ]
            json.dump(serializable_reranked, f, indent=2, ensure_ascii=False)

        # 保存评估结果
        with open(f"{output_dir}/evaluation_results.json", 'w', encoding='utf-8') as f:
            json.dump(evaluation_results, f, indent=2, ensure_ascii=False)

        print(f"Results saved to {output_dir}/")

    def run_demo(self, limit_queries: int = 3, save_results: bool = True):
        """
        运行完整的演示

        Args:
            limit_queries: 限制处理的查询数量
            save_results: 是否保存结果
        """
        print("="*60)
        print("DL19 BM25 + LLM Reranking Demo")
        print("="*60)

        # 1. 加载 BM25 结果
        bm25_results = self.load_bm25_results()

        # 2. 执行 LLM 重排
        reranked_results = self.perform_llm_reranking(bm25_results, limit_queries)

        # 3. 创建评估用的相关性判断
        qrels = self.create_dummy_qrels()

        # 4. 评估结果
        evaluation_results = self.evaluate_results(bm25_results, reranked_results, qrels)

        # 5. 显示结果
        print_results_table(evaluation_results)

        # 6. 保存结果（如果需要）
        if save_results:
            self.save_results(bm25_results, reranked_results, evaluation_results)

        # 7. 显示一些示例
        self.show_examples(bm25_results, reranked_results, limit=2)

        print("\n" + "="*60)
        print("Demo completed!")
        print("="*60)

    def show_examples(self, bm25_results: Dict, reranked_results: Dict, limit: int = 2):
        """显示一些重排前后的示例"""
        print("\n" + "="*60)
        print("RERANKING EXAMPLES")
        print("="*60)

        count = 0
        for query_id in bm25_results:
            if count >= limit:
                break

            query_text = self.queries[query_id]
            bm25_docs = bm25_results[query_id]
            reranked_docs = reranked_results.get(query_id, bm25_docs)

            print(f"\nQuery {query_id}: {query_text}")
            print("-" * 40)

            print("BM25 Top-5:")
            for i, (doc_id, doc_text, score) in enumerate(bm25_docs[:5]):
                print(f"  {i+1}. [{doc_id}] {doc_text[:100]}... (score: {score:.3f})")

            print("\nLLM Reranked Top-5:")
            for i, (doc_id, doc_text, score) in enumerate(reranked_docs[:5]):
                print(f"  {i+1}. [{doc_id}] {doc_text[:100]}... (score: {score:.3f})")

            count += 1


def main():
    """主函数"""
    try:
        # 创建并运行演示
        demo = DL19Demo()
        demo.run_demo(limit_queries=3, save_results=True)

    except Exception as e:
        print(f"Error running demo: {e}")
        print("Please check your configuration and API key in .env file")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
