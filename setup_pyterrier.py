#!/usr/bin/env python3
"""
PyTerrier 环境设置脚本

自动安装和配置 PyTerrier 环境，包括 Java 依赖检查
"""

import os
import sys
import subprocess
import platform
from typing import Optional


class PyTerrierSetup:
    """PyTerrier 环境设置类"""
    
    def __init__(self):
        self.system = platform.system()
        self.java_version = None
    
    def check_java(self) -> bool:
        """检查 Java 环境"""
        print("☕ Checking Java environment...")
        
        try:
            # 检查 Java 版本
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # 解析 Java 版本
                version_output = result.stderr if result.stderr else result.stdout
                print(f"✅ Java found:")
                for line in version_output.split('\n')[:3]:
                    if line.strip():
                        print(f"   {line.strip()}")
                
                # 检查版本是否足够
                if 'version "1.8' in version_output or 'version "8' in version_output:
                    print("✅ Java 8+ detected")
                    return True
                elif any(f'version "{v}' in version_output for v in ['9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21']):
                    print("✅ Java 8+ detected")
                    return True
                else:
                    print("⚠️  Java version might be too old")
                    return True  # 尝试继续
            else:
                print("❌ Java not found or not working")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Java not found in PATH")
            return False
    
    def install_java_instructions(self):
        """提供 Java 安装说明"""
        print("\n📚 Java Installation Instructions:")
        print("=" * 40)
        
        if self.system == "Darwin":  # macOS
            print("🍎 macOS:")
            print("   Option 1 - Homebrew:")
            print("     brew install openjdk@11")
            print("   Option 2 - Download from Oracle:")
            print("     https://www.oracle.com/java/technologies/downloads/")
            
        elif self.system == "Linux":
            print("🐧 Linux:")
            print("   Ubuntu/Debian:")
            print("     sudo apt update")
            print("     sudo apt install openjdk-11-jdk")
            print("   CentOS/RHEL:")
            print("     sudo yum install java-11-openjdk-devel")
            
        elif self.system == "Windows":
            print("🪟 Windows:")
            print("   Option 1 - Download from Oracle:")
            print("     https://www.oracle.com/java/technologies/downloads/")
            print("   Option 2 - Use Chocolatey:")
            print("     choco install openjdk11")
        
        print("\n💡 After installation, restart your terminal and try again.")
    
    def check_python_version(self) -> bool:
        """检查 Python 版本"""
        print("🐍 Checking Python version...")
        
        version = sys.version_info
        print(f"   Python {version.major}.{version.minor}.{version.micro}")
        
        if version.major >= 3 and version.minor >= 7:
            print("✅ Python 3.7+ detected")
            return True
        else:
            print("❌ Python 3.7+ required")
            return False
    
    def install_pyterrier(self) -> bool:
        """安装 PyTerrier"""
        print("📦 Installing PyTerrier...")
        
        try:
            # 检查是否已安装
            import pyterrier as pt
            print("✅ PyTerrier already installed")
            return True
            
        except ImportError:
            print("🔄 Installing PyTerrier via pip...")
            
            try:
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', 
                    'python-terrier', '--quiet'
                ], check=True, timeout=300)
                
                print("✅ PyTerrier installed successfully")
                return True
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install PyTerrier: {e}")
                return False
            except subprocess.TimeoutExpired:
                print("❌ Installation timeout")
                return False
    
    def install_dependencies(self) -> bool:
        """安装其他依赖"""
        print("📦 Installing additional dependencies...")
        
        dependencies = [
            'pandas',
            'numpy', 
            'requests',
            'tqdm'
        ]
        
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install'
            ] + dependencies + ['--quiet'], check=True, timeout=180)
            
            print("✅ Dependencies installed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    def test_pyterrier(self) -> bool:
        """测试 PyTerrier 安装"""
        print("🧪 Testing PyTerrier installation...")
        
        try:
            import pyterrier as pt
            
            # 尝试初始化
            if not pt.started():
                pt.init()
            
            print("✅ PyTerrier initialization successful")
            
            # 显示版本信息
            print(f"   PyTerrier version: {pt.__version__}")
            
            return True
            
        except Exception as e:
            print(f"❌ PyTerrier test failed: {e}")
            return False
    
    def create_test_script(self):
        """创建测试脚本"""
        test_script = """#!/usr/bin/env python3
import pyterrier as pt

# 初始化 PyTerrier
if not pt.started():
    pt.init()

print("✅ PyTerrier is working!")
print(f"Version: {pt.__version__}")

# 创建简单的内存索引测试
docs = [
    {"docno": "d1", "text": "hello world"},
    {"docno": "d2", "text": "python information retrieval"},
    {"docno": "d3", "text": "pyterrier is awesome"}
]

# 构建内存索引
indexer = pt.IterDictIndexer("./test_index")
index_ref = indexer.index(docs)
index = pt.IndexFactory.of(index_ref)

print(f"📊 Test index created with {index.getCollectionStatistics().getNumberOfDocuments()} documents")

# 测试检索
bm25 = pt.BatchRetrieve(index, wmodel="BM25")
query = pt.new_query("hello")
results = bm25.search("hello")

print(f"🔍 Test search returned {len(results)} results")
print("🎉 PyTerrier setup test completed successfully!")
"""
        
        with open("test_pyterrier.py", 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print("✅ Created test script: test_pyterrier.py")
    
    def run_setup(self) -> bool:
        """运行完整设置"""
        print("🚀 PyTerrier Environment Setup")
        print("=" * 40)
        
        success_count = 0
        total_checks = 4
        
        # 1. 检查 Python 版本
        if self.check_python_version():
            success_count += 1
        
        # 2. 检查 Java 环境
        if self.check_java():
            success_count += 1
        else:
            self.install_java_instructions()
            return False
        
        # 3. 安装 PyTerrier
        if self.install_pyterrier():
            success_count += 1
        
        # 4. 安装依赖
        if self.install_dependencies():
            success_count += 1
        
        print(f"\n📊 Setup Summary: {success_count}/{total_checks} checks passed")
        
        if success_count == total_checks:
            # 5. 测试安装
            if self.test_pyterrier():
                print("\n🎉 PyTerrier setup completed successfully!")
                
                # 创建测试脚本
                self.create_test_script()
                
                print("\n📚 Next steps:")
                print("   1. Run the demo: python pyterrier_dl19_demo.py")
                print("   2. Test installation: python test_pyterrier.py")
                print("   3. Check PyTerrier docs: https://pyterrier.readthedocs.io/")
                
                return True
            else:
                print("\n❌ PyTerrier test failed")
                return False
        else:
            print("\n❌ Setup incomplete. Please resolve the issues above.")
            return False


def main():
    """主函数"""
    setup = PyTerrierSetup()
    
    print("This script will set up PyTerrier for the DL19 demo.")
    print("PyTerrier requires Java 8+ to be installed.\n")
    
    if setup.run_setup():
        print("\n✅ Ready to run PyTerrier DL19 demo!")
    else:
        print("\n❌ Setup failed. Please check the requirements and try again.")


if __name__ == "__main__":
    main()
