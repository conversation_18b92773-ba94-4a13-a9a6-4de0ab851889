import pyterrier as pt
import pandas as pd
import os
from typing import Dict, Any
import json

class DL19RetrieverSystem:
    def __init__(self, data_path: str = "./msmarco-passage"):
        """
        Initialize the DL19 retriever system
        
        Args:
            data_path: Path to store/load the MS MARCO passage collection
        """
        self.data_path = data_path
        self.index_path = os.path.join(data_path, "index")
        
        # Initialize PyTerrier if not already done
        if not pt.started():
            pt.init()
            
    def download_and_prepare_data(self):
        """Download MS MARCO passage collection and DL19 queries/qrels"""
        print("Downloading MS MARCO passage collection...")
        
        # Download the dataset
        dataset = pt.get_dataset("irds:msmarco-passage/trec-dl-2019/judged")
        
        # Get passages, queries, and qrels
        self.topics = dataset.get_topics()
        self.qrels = dataset.get_qrels()
        
        print(f"Loaded {len(self.topics)} queries")
        print(f"Loaded {len(self.qrels)} qrels")
        
        return dataset
    
    def build_index(self, dataset):
        """Build index for the MS MARCO passage collection"""
        print("Building index...")
        
        # Check if index already exists
        if os.path.exists(self.index_path):
            print("Index already exists. Loading...")
            return pt.IndexFactory.of(self.index_path)
        
        # Create indexer
        indexer = pt.IterDictIndexer(
            self.index_path,
            overwrite=True,
            meta={'docno': 20, 'text': 4096},
            verbose=True
        )
        
        # Get documents iterator
        docs_iter = dataset.get_corpus_iter()
        
        # Index documents
        index_ref = indexer.index(docs_iter)
        
        print("Indexing completed!")
        return pt.IndexFactory.of(index_ref)
    
    def create_bm25_retriever(self, index, k1: float = 1.2, b: float = 0.75):
        """
        Create BM25 retriever
        
        Args:
            index: PyTerrier index
            k1: BM25 k1 parameter
            b: BM25 b parameter
            
        Returns:
            BM25 retriever pipeline
        """
        print(f"Creating BM25 retriever with k1={k1}, b={b}")
        
        # Create BM25 retriever
        bm25 = pt.BatchRetrieve(
            index,
            wmodel="BM25",
            controls={"c": b, "k1": k1},
            verbose=True
        )
        
        return bm25
    
    def run_retrieval(self, retriever, num_results: int = 1000):
        """
        Run retrieval on DL19 queries
        
        Args:
            retriever: PyTerrier retriever
            num_results: Number of results to retrieve per query
            
        Returns:
            Retrieved results dataframe
        """
        print(f"Running retrieval for {len(self.topics)} queries...")
        
        # Set number of results
        retriever.controls["end"] = num_results
        
        # Run retrieval
        results = retriever.transform(self.topics)
        
        print(f"Retrieved {len(results)} total results")
        return results
    
    def evaluate_results(self, results):
        """
        Evaluate retrieval results
        
        Args:
            results: Retrieved results dataframe
            
        Returns:
            Dictionary of evaluation metrics
        """
        print("Evaluating results...")
        
        # Define evaluation metrics
        eval_metrics = [
            "map",           # Mean Average Precision
            "ndcg",          # NDCG@10
            "ndcg_cut_20",   # NDCG@20
            "P_10",          # Precision@10
            "P_20",          # Precision@20
            "recall_1000",   # Recall@1000
            "mrt"            # Mean Reciprocal Rank
        ]
        
        # Run evaluation
        eval_results = pt.Experiment(
            [results],
            self.topics,
            self.qrels,
            eval_metrics,
            names=["BM25"]
        )
        
        return eval_results
    
    def parameter_tuning(self, index):
        """
        Tune BM25 parameters using grid search
        
        Args:
            index: PyTerrier index
            
        Returns:
            Best parameters and results
        """
        print("Starting parameter tuning...")
        
        # Define parameter grid
        k1_values = [0.5, 0.9, 1.2, 1.5, 2.0]
        b_values = [0.3, 0.5, 0.75, 0.8, 0.9]
        
        best_score = 0
        best_params = {}
        results_list = []
        
        for k1 in k1_values:
            for b in b_values:
                # Create retriever with current parameters
                bm25 = self.create_bm25_retriever(index, k1=k1, b=b)
                
                # Run retrieval
                results = self.run_retrieval(bm25, num_results=1000)
                
                # Evaluate
                eval_dict = pt.Utils.evaluate(
                    results,
                    self.qrels,
                    metrics=["ndcg_cut_20"]
                )
                
                ndcg20 = eval_dict["ndcg_cut_20"]
                
                print(f"k1={k1}, b={b}: NDCG@20={ndcg20:.4f}")
                
                results_list.append({
                    "k1": k1,
                    "b": b,
                    "ndcg@20": ndcg20
                })
                
                if ndcg20 > best_score:
                    best_score = ndcg20
                    best_params = {"k1": k1, "b": b}
        
        print(f"\nBest parameters: k1={best_params['k1']}, b={best_params['b']}")
        print(f"Best NDCG@20: {best_score:.4f}")
        
        return best_params, pd.DataFrame(results_list)
    
    def save_results(self, results, eval_results, output_path: str = "./results"):
        """Save retrieval results and evaluation metrics"""
        os.makedirs(output_path, exist_ok=True)
        
        # Save retrieval results
        results.to_csv(
            os.path.join(output_path, "dl19_bm25_results.csv"),
            index=False
        )
        
        # Save evaluation results
        eval_results.to_csv(
            os.path.join(output_path, "dl19_evaluation_results.csv")
        )
        
        print(f"Results saved to {output_path}")
    
    def run_complete_pipeline(self, tune_parameters: bool = False):
        """Run the complete retrieval pipeline"""
        print("Starting DL19 retrieval pipeline...")
        
        # Step 1: Download and prepare data
        dataset = self.download_and_prepare_data()
        
        # Step 2: Build index
        index = self.build_index(dataset)
        
        # Step 3: Parameter tuning (optional)
        if tune_parameters:
            best_params, tuning_results = self.parameter_tuning(index)
            k1 = best_params["k1"]
            b = best_params["b"]
            
            # Save tuning results
            tuning_results.to_csv("bm25_parameter_tuning_results.csv", index=False)
        else:
            # Use default parameters
            k1 = 1.2
            b = 0.75
        
        # Step 4: Create BM25 retriever with best parameters
        bm25 = self.create_bm25_retriever(index, k1=k1, b=b)
        
        # Step 5: Run retrieval
        results = self.run_retrieval(bm25, num_results=1000)
        
        # Step 6: Evaluate
        eval_results = self.evaluate_results(results)
        
        # Step 7: Save results
        self.save_results(results, eval_results)
        
        # Print final evaluation metrics
        print("\n" + "="*50)
        print("FINAL EVALUATION RESULTS:")
        print("="*50)
        print(eval_results)
        
        return results, eval_results

def main():
    """Main function to run the DL19 retrieval system"""
    # Initialize system
    retriever_system = DL19RetrieverSystem()
    
    # Run complete pipeline with parameter tuning
    results, eval_results = retriever_system.run_complete_pipeline(
        tune_parameters=True  # Set to False to use default BM25 parameters
    )
    
    # Additional analysis
    print("\n" + "="*50)
    print("ADDITIONAL ANALYSIS:")
    print("="*50)
    
    # Query-level performance analysis
    query_performance = results.groupby('qid').agg({
        'score': ['mean', 'std', 'count']
    })
    print("\nQuery-level statistics:")
    print(query_performance.head(10))
    
    # Save run file in TREC format
    pt.io.write_results(
        results,
        "dl19_bm25_run.txt",
        format="trec"
    )
    print("\nTREC run file saved as 'dl19_bm25_run.txt'")

if __name__ == "__main__":
    main()
