#!/usr/bin/env python3
"""
PyTerrier Installation Test

Test script to verify PyTerrier installation and functionality
"""

import sys
import traceback


def test_pyterrier_import():
    """Test PyTerrier import"""
    print("🔍 Testing PyTerrier import...")
    
    try:
        import pyterrier as pt
        print("✅ PyTerrier imported successfully")
        print(f"   Version: {pt.__version__}")
        return True, pt
    except ImportError as e:
        print(f"❌ PyTerrier import failed: {e}")
        return False, None
    except Exception as e:
        print(f"❌ Unexpected error importing PyTerrier: {e}")
        return False, None


def test_pyterrier_init(pt):
    """Test PyTerrier initialization"""
    print("\n🔍 Testing PyTerrier initialization...")
    
    if pt.started():
        print("✅ PyTerrier already initialized")
        return True
    
    try:
        # Method 1: Standard initialization
        print("   Trying standard initialization...")
        pt.init()
        print("✅ PyTerrier initialized successfully")
        return True
        
    except Exception as e:
        print(f"⚠️  Standard initialization failed: {e}")
        
        try:
            # Method 2: Initialize with specific version
            print("   Trying initialization with specific version...")
            pt.init(version="5.7", helper_version="0.0.7")
            print("✅ PyTerrier initialized with specific version")
            return True
            
        except Exception as e2:
            print(f"⚠️  Version-specific initialization failed: {e2}")
            
            try:
                # Method 3: Initialize without helper
                print("   Trying initialization without helper...")
                pt.init(boot_packages=["com.github.terrierteam:terrier-core:5.7"])
                print("✅ PyTerrier initialized without helper")
                return True
                
            except Exception as e3:
                print(f"❌ All initialization methods failed: {e3}")
                print("\nDetailed error:")
                traceback.print_exc()
                return False


def test_basic_functionality(pt):
    """Test basic PyTerrier functionality"""
    print("\n🔍 Testing basic functionality...")
    
    try:
        # Test creating a simple index
        docs = [
            {"docno": "d1", "text": "hello world"},
            {"docno": "d2", "text": "python information retrieval"},
            {"docno": "d3", "text": "pyterrier is awesome"}
        ]
        
        print("   Creating test index...")
        indexer = pt.IterDictIndexer("./test_index", overwrite=True)
        index_ref = indexer.index(docs)
        index = pt.IndexFactory.of(index_ref)
        
        print("   Testing retrieval...")
        bm25 = pt.BatchRetrieve(index, wmodel="BM25")
        
        import pandas as pd
        query_df = pd.DataFrame([{"qid": "1", "query": "hello"}])
        results = bm25.transform(query_df)
        
        print(f"✅ Basic functionality test passed")
        print(f"   Index created with {index.getCollectionStatistics().getNumberOfDocuments()} documents")
        print(f"   Search returned {len(results)} results")
        
        # Cleanup
        import shutil
        import os
        if os.path.exists("./test_index"):
            shutil.rmtree("./test_index")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False


def test_java_environment():
    """Test Java environment"""
    print("\n🔍 Testing Java environment...")
    
    import subprocess
    
    try:
        result = subprocess.run(['java', '-version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_output = result.stderr if result.stderr else result.stdout
            print("✅ Java is available:")
            for line in version_output.split('\n')[:2]:
                if line.strip():
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ Java command failed")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Java command timed out")
        return False
    except FileNotFoundError:
        print("❌ Java not found in PATH")
        return False
    except Exception as e:
        print(f"❌ Error checking Java: {e}")
        return False


def provide_installation_help():
    """Provide installation help"""
    print("\n" + "=" * 60)
    print("🔧 INSTALLATION HELP")
    print("=" * 60)
    
    print("\n📋 If PyTerrier installation failed, try these solutions:")
    
    print("\n1. 🐍 Use compatible Python version:")
    print("   conda create -n pyterrier python=3.11")
    print("   conda activate pyterrier")
    print("   pip install python-terrier")
    
    print("\n2. ☕ Install Java (if missing):")
    print("   # macOS")
    print("   brew install openjdk@11")
    print("   # Ubuntu/Debian")
    print("   sudo apt install openjdk-11-jdk")
    
    print("\n3. 🔧 Fix compilation issues:")
    print("   pip install --upgrade pip setuptools wheel")
    print("   pip install Cython")
    print("   pip install pyjnius --no-cache-dir")
    print("   pip install python-terrier")
    
    print("\n4. 🐳 Use Docker (most reliable):")
    print("   docker run -it --rm -v $(pwd):/app python:3.11")
    print("   # Inside container:")
    print("   apt-get update && apt-get install -y openjdk-11-jdk")
    print("   pip install python-terrier")
    
    print("\n5. 🔄 Use alternative (pure Python):")
    print("   pip install rank-bm25")
    print("   python alternative_search_demo.py")


def main():
    """Main test function"""
    print("🧪 PyTerrier Installation Test")
    print("=" * 50)
    
    # Test system info
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    
    # Test Java
    java_ok = test_java_environment()
    
    # Test PyTerrier import
    pt_imported, pt = test_pyterrier_import()
    
    if not pt_imported:
        provide_installation_help()
        return
    
    # Test PyTerrier initialization
    pt_initialized = test_pyterrier_init(pt)
    
    if not pt_initialized:
        provide_installation_help()
        return
    
    # Test basic functionality
    functionality_ok = test_basic_functionality(pt)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    print(f"Java environment:     {'✅ PASS' if java_ok else '❌ FAIL'}")
    print(f"PyTerrier import:     {'✅ PASS' if pt_imported else '❌ FAIL'}")
    print(f"PyTerrier init:       {'✅ PASS' if pt_initialized else '❌ FAIL'}")
    print(f"Basic functionality:  {'✅ PASS' if functionality_ok else '❌ FAIL'}")
    
    if all([java_ok, pt_imported, pt_initialized, functionality_ok]):
        print("\n🎉 All tests passed! PyTerrier is ready to use.")
        print("\nYou can now run:")
        print("   python pyterrier_dl19_retrieval.py")
    else:
        print("\n⚠️  Some tests failed. See installation help above.")
        print("\nAlternative: Use the pure Python demo:")
        print("   pip install rank-bm25")
        print("   python alternative_search_demo.py")


if __name__ == "__main__":
    main()
