# DL19 BM25 + LLM 重排演示

这个演示展示了如何在 TREC Deep Learning 2019 数据集上使用 BM25 检索结合大语言模型重排来提升检索性能。

## 功能特性

- ✅ 使用现有的 DL19 BM25 top-20 结果作为基线
- ✅ 支持 OpenRouter API 调用各种大语言模型
- ✅ 支持代理配置（适合国内用户）
- ✅ 实现 RankGPT 风格的列表式重排
- ✅ 提供完整的评估指标（NDCG, MAP, Precision, Recall）
- ✅ 结果可视化和对比分析
- ✅ 保存详细的实验结果

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 复制环境配置文件
cp .env.example .env
```

### 2. 配置 API Key

编辑 `.env` 文件，填入你的 OpenRouter API key：

```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Model Configuration
DEFAULT_MODEL=openai/gpt-4-1106-preview

# Proxy Configuration (如果需要)
HTTP_PROXY=http://127.0.0.1:1087
HTTPS_PROXY=http://127.0.0.1:1087
```

### 3. 运行演示

```bash
# 基本运行（处理3个查询）
python run_demo.py

# 处理更多查询
python run_demo.py --queries 5

# 使用不同模型
python run_demo.py --model "anthropic/claude-3-sonnet"

# 不保存结果文件
python run_demo.py --no-save

# 高级用法 - 测试不同配置
python run_demo_advanced.py --approach rel-gen --queries 5
python run_demo_advanced.py --window-size 10 --step 5
python run_demo_advanced.py --model anthropic/claude-3-sonnet

# 分析结果
python analyze_results.py
python analyze_results.py --save-plot results_comparison.png
```

### 4. 改进的代码使用

我们提供了三种层次的使用方式：

#### 🚀 快速开始（推荐新手）
```python
from quick_start_example import simple_rerank

# 一行代码完成重排
reranked_docs, indices = simple_rerank(
    query="机器学习算法",
    candidates=["文档1", "文档2", "文档3"],
    approach="rankgpt"
)
```

#### 🔧 配置化使用（推荐进阶用户）
```python
from reranker_config import create_reranker_from_config

# 使用预定义配置
reranker = create_reranker_from_config('rankgpt_balanced')

# 执行重排
reranked_docs, indices = reranker.rerank(
    query="你的查询",
    candidates=["文档1", "文档2", "文档3"],
    return_indices=True
)
```

#### ⚙️ 完全自定义（推荐专业用户）
```python
from improved_reranker_example import ImprovedReranker

# 完全自定义配置
reranker = ImprovedReranker(
    reranking_approach="rankgpt",
    model_name="openai/gpt-4-1106-preview",
    proxy_config={"http": "http://127.0.0.1:1087"}
)

# 高级功能
comparison = reranker.compare_with_baseline(
    query="查询文本",
    candidates=documents,
    top_k=5
)
```

## 演示流程

### 1. 数据加载
- 从 `toy_data/dl19_bm25_top20.jsonl` 加载 BM25 检索结果
- 每个查询包含 top-20 个相关文档
- 提取查询文本和文档内容

### 2. LLM 重排
- 使用 RankGPT 方法对每个查询的 top-20 文档进行重排
- 支持多种大语言模型（通过 OpenRouter）
- 自动处理 API 调用和错误恢复

### 3. 性能评估
- 计算 NDCG@1, NDCG@5, NDCG@10, NDCG@20
- 计算 Precision@1, Precision@5, Precision@10, Precision@20  
- 计算 Recall@1, Recall@5, Recall@10, Recall@20
- 计算 MAP (Mean Average Precision)

### 4. 结果展示
- 对比 BM25 基线和 LLM 重排结果
- 显示性能提升百分比
- 展示具体的重排示例

## 输出示例

```
==============================================================
EVALUATION RESULTS
==============================================================
Metric          BM25 Baseline   LLM Reranked    Improvement
--------------------------------------------------------------
NDCG@1          1.0000          1.0000          0.00           %
P@1             1.0000          1.0000          0.00           %
Recall@1        0.0667          0.0667          -0.00          %
NDCG@5          1.0000          0.7417          -25.83         %
P@5             1.0000          0.9200          -8.00          %
Recall@5        0.3333          0.3067          -8.00          %
NDCG@10         1.0000          0.7535          -24.65         %
P@10            1.0000          0.8000          -20.00         %
Recall@10       0.6667          0.5333          -20.00         %
NDCG@20         1.0000          0.8983          -10.17         %
P@20            0.7500          0.7500          0.00           %
Recall@20       1.0000          1.0000          0.00           %
MAP             1.0000          0.8691          -13.09         %
==============================================================
```

### 结果解读

上述结果显示 LLM 重排在某些指标上表现不如 BM25 基线，这是正常现象，原因包括：

1. **评估偏差**：使用基于 BM25 分数的虚拟相关性判断会偏向 BM25
2. **参数调优**：默认参数可能不是最优的
3. **模型选择**：不同模型的重排能力差异较大
4. **数据特性**：某些查询类型可能更适合传统检索方法

## 文件结构

```
.
├── demo_dl19_bm25_llm_rerank.py    # 主演示脚本
├── run_demo.py                     # 简化运行脚本
├── config.py                       # 配置管理
├── .env.example                    # 环境配置模板
├── toy_data/
│   └── dl19_bm25_top20.jsonl      # DL19 BM25 结果数据
├── src/llm4ranking/               # 核心库代码
│   ├── lm/                        # 语言模型接口
│   │   ├── openrouter.py         # OpenRouter 客户端
│   │   └── ...
│   ├── datasets/                  # 数据集加载器
│   ├── evaluation/                # 评估模块
│   └── ...
└── results/                       # 输出结果目录
    ├── bm25_results.json
    ├── reranked_results.json
    └── evaluation_results.json
```

## 支持的模型

通过 OpenRouter API，支持以下模型：
- `openai/gpt-4-1106-preview`
- `openai/gpt-3.5-turbo`
- `anthropic/claude-3-sonnet`
- `anthropic/claude-3-haiku`
- `meta-llama/llama-2-70b-chat`
- 更多模型请参考 [OpenRouter 文档](https://openrouter.ai/docs)

## 高级配置和优化

### 1. 使用高级演示脚本

```bash
# 测试不同重排方法
python run_demo_advanced.py --approach rel-gen --queries 5
python run_demo_advanced.py --approach prp-heap --queries 3

# 调整窗口参数
python run_demo_advanced.py --window-size 10 --step 5 --queries 5

# 使用不同模型
python run_demo_advanced.py --model anthropic/claude-3-sonnet --queries 3
python run_demo_advanced.py --model openai/gpt-3.5-turbo --queries 5

# 调整文档截断长度
python run_demo_advanced.py --truncate-length 500 --queries 3
```

### 2. 结果分析

```bash
# 分析重排效果
python analyze_results.py

# 生成可视化图表
python analyze_results.py --save-plot comparison.png

# 分析特定结果目录
python analyze_results.py --results-dir my_results/
```

### 3. 性能优化建议

基于你的结果，以下是一些优化建议：

**如果 NDCG@5/NDCG@10 下降较多：**
- 尝试更小的窗口大小：`--window-size 10`
- 减少步长：`--step 5`
- 使用不同的重排方法：`--approach rel-gen`

**如果 Precision 指标下降：**
- 增加文档截断长度：`--truncate-length 500`
- 使用更强的模型：`--model openai/gpt-4`

**如果 MAP 下降：**
- 尝试成对比较方法：`--approach prp-heap`
- 调整重排参数组合

## 注意事项

1. **API 费用**: 使用 OpenRouter API 会产生费用，建议先用少量查询测试
2. **代理设置**: 如果在国内使用，请正确配置代理
3. **相关性判断**: 演示中使用的是基于 BM25 分数的虚拟相关性判断，实际应用中应使用人工标注的 qrels
4. **模型选择**: 不同模型的重排效果可能差异较大，建议多试几个模型

## 故障排除

### 常见问题

1. **API Key 错误**
   - 检查 `.env` 文件中的 `OPENROUTER_API_KEY` 是否正确
   - 确认 API key 有足够的余额

2. **网络连接问题**
   - 检查代理设置是否正确
   - 尝试直接访问 OpenRouter API

3. **模型不可用**
   - 某些模型可能暂时不可用
   - 尝试使用其他模型

4. **内存不足**
   - 减少 `limit_queries` 参数
   - 使用更小的模型

### 获取帮助

如果遇到问题，请检查：
1. 环境配置是否正确
2. 网络连接是否正常
3. API key 是否有效
4. 模型是否可用
