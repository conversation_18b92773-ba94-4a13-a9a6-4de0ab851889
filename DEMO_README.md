# DL19 BM25 + LLM 重排演示

这个演示展示了如何在 TREC Deep Learning 2019 数据集上使用 BM25 检索结合大语言模型重排来提升检索性能。

## 功能特性

- ✅ 使用现有的 DL19 BM25 top-20 结果作为基线
- ✅ 支持 OpenRouter API 调用各种大语言模型
- ✅ 支持代理配置（适合国内用户）
- ✅ 实现 RankGPT 风格的列表式重排
- ✅ 提供完整的评估指标（NDCG, MAP, Precision, Recall）
- ✅ 结果可视化和对比分析
- ✅ 保存详细的实验结果

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 复制环境配置文件
cp .env.example .env
```

### 2. 配置 API Key

编辑 `.env` 文件，填入你的 OpenRouter API key：

```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Model Configuration
DEFAULT_MODEL=openai/gpt-4-1106-preview

# Proxy Configuration (如果需要)
HTTP_PROXY=http://127.0.0.1:1087
HTTPS_PROXY=http://127.0.0.1:1087
```

### 3. 运行演示

```bash
# 基本运行（处理3个查询）
python run_demo.py

# 处理更多查询
python run_demo.py --queries 5

# 使用不同模型
python run_demo.py --model "anthropic/claude-3-sonnet"

# 不保存结果文件
python run_demo.py --no-save
```

## 演示流程

### 1. 数据加载
- 从 `toy_data/dl19_bm25_top20.jsonl` 加载 BM25 检索结果
- 每个查询包含 top-20 个相关文档
- 提取查询文本和文档内容

### 2. LLM 重排
- 使用 RankGPT 方法对每个查询的 top-20 文档进行重排
- 支持多种大语言模型（通过 OpenRouter）
- 自动处理 API 调用和错误恢复

### 3. 性能评估
- 计算 NDCG@1, NDCG@5, NDCG@10, NDCG@20
- 计算 Precision@1, Precision@5, Precision@10, Precision@20  
- 计算 Recall@1, Recall@5, Recall@10, Recall@20
- 计算 MAP (Mean Average Precision)

### 4. 结果展示
- 对比 BM25 基线和 LLM 重排结果
- 显示性能提升百分比
- 展示具体的重排示例

## 输出示例

```
==============================================================
EVALUATION RESULTS
==============================================================
Metric          BM25 Baseline   LLM Reranked    Improvement    
--------------------------------------------------------------
NDCG@1          0.6667          0.8333          25.00%
NDCG@5          0.7234          0.8456          16.89%
NDCG@10         0.7456          0.8234          10.44%
NDCG@20         0.7234          0.7890          9.07%
P@1             0.6667          0.8333          25.00%
P@5             0.7333          0.8000          9.09%
P@10            0.7000          0.7500          7.14%
P@20            0.6750          0.7000          3.70%
MAP             0.7123          0.7845          10.14%
==============================================================
```

## 文件结构

```
.
├── demo_dl19_bm25_llm_rerank.py    # 主演示脚本
├── run_demo.py                     # 简化运行脚本
├── config.py                       # 配置管理
├── .env.example                    # 环境配置模板
├── toy_data/
│   └── dl19_bm25_top20.jsonl      # DL19 BM25 结果数据
├── src/llm4ranking/               # 核心库代码
│   ├── lm/                        # 语言模型接口
│   │   ├── openrouter.py         # OpenRouter 客户端
│   │   └── ...
│   ├── datasets/                  # 数据集加载器
│   ├── evaluation/                # 评估模块
│   └── ...
└── results/                       # 输出结果目录
    ├── bm25_results.json
    ├── reranked_results.json
    └── evaluation_results.json
```

## 支持的模型

通过 OpenRouter API，支持以下模型：
- `openai/gpt-4-1106-preview`
- `openai/gpt-3.5-turbo`
- `anthropic/claude-3-sonnet`
- `anthropic/claude-3-haiku`
- `meta-llama/llama-2-70b-chat`
- 更多模型请参考 [OpenRouter 文档](https://openrouter.ai/docs)

## 自定义配置

### 修改重排方法
在 `demo_dl19_bm25_llm_rerank.py` 中修改：
```python
self.reranker = Reranker(
    reranking_approach="rankgpt",  # 可选: "rel-gen", "query-gen", "prp-heap" 等
    model_type="openrouter",
    model_name=Config.DEFAULT_MODEL,
    model_args=model_args
)
```

### 修改评估指标
在 `evaluate_results` 方法中修改 `k_values` 参数：
```python
comparison_results = self.evaluator.compare_runs(
    baseline_results=bm25_converted,
    reranked_results=reranked_converted,
    qrels=qrels,
    k_values=[1, 3, 5, 10, 20]  # 自定义 k 值
)
```

## 注意事项

1. **API 费用**: 使用 OpenRouter API 会产生费用，建议先用少量查询测试
2. **代理设置**: 如果在国内使用，请正确配置代理
3. **相关性判断**: 演示中使用的是基于 BM25 分数的虚拟相关性判断，实际应用中应使用人工标注的 qrels
4. **模型选择**: 不同模型的重排效果可能差异较大，建议多试几个模型

## 故障排除

### 常见问题

1. **API Key 错误**
   - 检查 `.env` 文件中的 `OPENROUTER_API_KEY` 是否正确
   - 确认 API key 有足够的余额

2. **网络连接问题**
   - 检查代理设置是否正确
   - 尝试直接访问 OpenRouter API

3. **模型不可用**
   - 某些模型可能暂时不可用
   - 尝试使用其他模型

4. **内存不足**
   - 减少 `limit_queries` 参数
   - 使用更小的模型

### 获取帮助

如果遇到问题，请检查：
1. 环境配置是否正确
2. 网络连接是否正常
3. API key 是否有效
4. 模型是否可用
