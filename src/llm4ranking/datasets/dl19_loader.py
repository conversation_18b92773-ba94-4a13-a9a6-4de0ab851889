import json
import os
from typing import Dict, List, Tuple, Optional
from datasets import load_dataset
import requests
from tqdm import tqdm


class DL19Dataset:
    """TREC Deep Learning 2019 dataset loader"""
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize DL19 dataset loader
        
        Args:
            data_dir: Directory to store data files
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        self.queries = {}
        self.documents = {}
        self.qrels = {}
        self.loaded = False
    
    def load_from_toy_data(self, toy_data_path: str = "toy_data/dl19_bm25_top20.jsonl"):
        """
        Load data from toy data file (for quick testing)
        
        Args:
            toy_data_path: Path to toy data file
        """
        print(f"Loading toy data from {toy_data_path}")
        
        with open(toy_data_path, 'r', encoding='utf-8') as f:
            data = [json.loads(line) for line in f]
        
        # Extract queries and documents
        for item in data:
            query_id = str(item['hits'][0]['qid'])  # Assuming all hits have same qid
            self.queries[query_id] = item['query']
            
            for hit in item['hits']:
                doc_id = hit['docid']
                self.documents[doc_id] = hit['content']
        
        # Create dummy qrels (we don't have ground truth in toy data)
        for query_id in self.queries:
            self.qrels[query_id] = {}
        
        self.loaded = True
        print(f"Loaded {len(self.queries)} queries and {len(self.documents)} documents from toy data")
    
    def load_from_huggingface(self):
        """Load DL19 data from HuggingFace datasets"""
        try:
            print("Loading DL19 data from HuggingFace...")
            
            # Load from the existing dataset used in the evaluation
            data = load_dataset("liuqi6777/pyserini_retrieval_results", 
                              data_files="bm25/dl19_top100.jsonl", 
                              split="train")
            
            # Process the data
            for item in data:
                query_id = item['qid']
                self.queries[query_id] = item['query']
                
                # Extract documents from hits
                for hit in item['hits']:
                    doc_id = hit['docid']
                    self.documents[doc_id] = hit['content']
            
            # Load qrels if available (this might need adjustment based on actual data structure)
            try:
                qrels_data = load_dataset("liuqi6777/pyserini_retrieval_results", 
                                        data_files="qrels/dl19.jsonl", 
                                        split="train")
                for item in qrels_data:
                    query_id = item['qid']
                    if query_id not in self.qrels:
                        self.qrels[query_id] = {}
                    self.qrels[query_id][item['docid']] = item['relevance']
            except:
                print("Warning: Could not load qrels, creating empty qrels")
                for query_id in self.queries:
                    self.qrels[query_id] = {}
            
            self.loaded = True
            print(f"Loaded {len(self.queries)} queries and {len(self.documents)} documents from HuggingFace")
            
        except Exception as e:
            print(f"Error loading from HuggingFace: {e}")
            print("Falling back to toy data...")
            self.load_from_toy_data()
    
    def load_msmarco_documents(self, limit: Optional[int] = None):
        """
        Load MS MARCO document collection (for full retrieval)
        
        Args:
            limit: Limit number of documents to load (for testing)
        """
        print("Loading MS MARCO document collection...")
        
        try:
            # This would load the full MS MARCO collection
            # For demo purposes, we'll use a subset or mock this
            docs_data = load_dataset("microsoft/ms_marco", "v2.1", split="train")
            
            count = 0
            for item in tqdm(docs_data):
                if limit and count >= limit:
                    break
                
                doc_id = item['docid']
                self.documents[doc_id] = item['passage']
                count += 1
            
            print(f"Loaded {len(self.documents)} documents from MS MARCO")
            
        except Exception as e:
            print(f"Error loading MS MARCO documents: {e}")
            print("Using existing documents from toy data")
    
    def get_queries(self) -> Dict[str, str]:
        """Get all queries"""
        if not self.loaded:
            self.load_from_toy_data()
        return self.queries.copy()
    
    def get_documents(self) -> Dict[str, str]:
        """Get all documents"""
        if not self.loaded:
            self.load_from_toy_data()
        return self.documents.copy()
    
    def get_qrels(self) -> Dict[str, Dict[str, int]]:
        """Get relevance judgments"""
        if not self.loaded:
            self.load_from_toy_data()
        return self.qrels.copy()
    
    def get_query_document_pairs(self, query_id: str, top_k: int = 20) -> Tuple[str, List[str]]:
        """
        Get query and top-k documents for reranking
        
        Args:
            query_id: Query ID
            top_k: Number of top documents to return
            
        Returns:
            Tuple of (query_text, list_of_document_texts)
        """
        if not self.loaded:
            self.load_from_toy_data()
        
        if query_id not in self.queries:
            raise ValueError(f"Query ID {query_id} not found")
        
        query_text = self.queries[query_id]
        
        # For toy data, we'll return available documents
        # In a real scenario, this would be the top-k from initial retrieval
        doc_texts = list(self.documents.values())[:top_k]
        
        return query_text, doc_texts
    
    def save_results(self, results: Dict, output_path: str):
        """Save results to file"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
