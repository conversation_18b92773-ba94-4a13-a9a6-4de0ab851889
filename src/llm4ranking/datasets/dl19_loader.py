import json
import os
import requests
from typing import Dict, <PERSON>, Tuple, Optional
from urllib.parse import urlparse


class DL19Dataset:
    """TREC Deep Learning 2019 dataset loader"""
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize DL19 dataset loader
        
        Args:
            data_dir: Directory to store data files
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        self.queries = {}
        self.documents = {}
        self.qrels = {}
        self.loaded = False
    
    def load_from_toy_data(self, toy_data_path: str = "toy_data/dl19_bm25_top20.jsonl"):
        """
        Load data from toy data file (for quick testing)
        
        Args:
            toy_data_path: Path to toy data file
        """
        print(f"Loading toy data from {toy_data_path}")
        
        with open(toy_data_path, 'r', encoding='utf-8') as f:
            data = [json.loads(line) for line in f]
        
        # Extract queries and documents
        for item in data:
            query_id = str(item['hits'][0]['qid'])  # Assuming all hits have same qid
            self.queries[query_id] = item['query']
            
            for hit in item['hits']:
                doc_id = hit['docid']
                self.documents[doc_id] = hit['content']
        
        # Create dummy qrels (we don't have ground truth in toy data)
        for query_id in self.queries:
            self.qrels[query_id] = {}
        
        self.loaded = True
        print(f"Loaded {len(self.queries)} queries and {len(self.documents)} documents from toy data")
    
    def load_official_qrels(self, qrels_path: str = None) -> Dict[str, Dict[str, int]]:
        """
        Load official TREC DL 2019 qrels

        Args:
            qrels_path: Path to qrels file, if None will try to download

        Returns:
            Official qrels dictionary
        """
        if qrels_path is None:
            qrels_path = os.path.join(self.data_dir, "2019qrels-docs.txt")

        # Try to download if file doesn't exist
        if not os.path.exists(qrels_path):
            print("Downloading official TREC DL 2019 qrels...")
            try:
                url = "https://trec.nist.gov/data/deep/2019qrels-docs.txt"
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                os.makedirs(os.path.dirname(qrels_path), exist_ok=True)
                with open(qrels_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"✅ Downloaded qrels to {qrels_path}")

            except Exception as e:
                print(f"❌ Failed to download qrels: {e}")
                print("Using dummy qrels instead")
                return self._create_dummy_qrels()

        # Load qrels file
        qrels = {}
        try:
            with open(qrels_path, 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 4:
                        qid, _, docid, rel = parts[0], parts[1], parts[2], int(parts[3])
                        if qid not in qrels:
                            qrels[qid] = {}
                        qrels[qid][docid] = rel

            print(f"✅ Loaded official qrels for {len(qrels)} queries")
            return qrels

        except Exception as e:
            print(f"❌ Error loading qrels: {e}")
            return self._create_dummy_qrels()

    def _create_dummy_qrels(self) -> Dict[str, Dict[str, int]]:
        """Create dummy qrels as fallback"""
        print("⚠️  Creating dummy qrels (biased toward BM25)")

        qrels = {}
        with open("toy_data/dl19_bm25_top20.jsonl", 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line)
                query_id = str(item['hits'][0]['qid'])

                qrels[query_id] = {}
                for i, hit in enumerate(item['hits']):
                    doc_id = hit['docid']
                    if i < 5:
                        qrels[query_id][doc_id] = 3
                    elif i < 10:
                        qrels[query_id][doc_id] = 2
                    elif i < 15:
                        qrels[query_id][doc_id] = 1
                    else:
                        qrels[query_id][doc_id] = 0

        return qrels

    def load_from_huggingface(self):
        """Load DL19 data from HuggingFace datasets - fallback to toy data"""
        print("Using toy data instead of HuggingFace for this demo...")
        self.load_from_toy_data()
    
    def load_msmarco_documents(self, limit: Optional[int] = None):
        """
        Load MS MARCO document collection (for full retrieval)
        For demo purposes, we use the documents already loaded from toy data
        """
        _ = limit  # Suppress unused parameter warning
        print("Using documents from toy data (no additional MS MARCO loading needed)")
    
    def get_queries(self) -> Dict[str, str]:
        """Get all queries"""
        if not self.loaded:
            self.load_from_toy_data()
        return self.queries.copy()
    
    def get_documents(self) -> Dict[str, str]:
        """Get all documents"""
        if not self.loaded:
            self.load_from_toy_data()
        return self.documents.copy()
    
    def get_qrels(self, use_official: bool = False) -> Dict[str, Dict[str, int]]:
        """
        Get relevance judgments

        Args:
            use_official: If True, try to load official TREC DL 2019 qrels

        Returns:
            Dictionary of relevance judgments
        """
        if not self.loaded:
            self.load_from_toy_data()

        if use_official:
            print("🔄 Loading official TREC DL 2019 qrels...")
            official_qrels = self.load_official_qrels()

            # 过滤只包含我们数据中的查询
            filtered_qrels = {}
            for query_id in self.queries:
                if query_id in official_qrels:
                    filtered_qrels[query_id] = official_qrels[query_id]
                else:
                    # 如果官方 qrels 中没有这个查询，使用 dummy qrels
                    filtered_qrels[query_id] = self.qrels.get(query_id, {})

            print(f"✅ Using official qrels for {len([q for q in filtered_qrels if filtered_qrels[q]])} queries")
            return filtered_qrels

        return self.qrels.copy()
    
    def get_query_document_pairs(self, query_id: str, top_k: int = 20) -> Tuple[str, List[str]]:
        """
        Get query and top-k documents for reranking
        
        Args:
            query_id: Query ID
            top_k: Number of top documents to return
            
        Returns:
            Tuple of (query_text, list_of_document_texts)
        """
        if not self.loaded:
            self.load_from_toy_data()
        
        if query_id not in self.queries:
            raise ValueError(f"Query ID {query_id} not found")
        
        query_text = self.queries[query_id]
        
        # For toy data, we'll return available documents
        # In a real scenario, this would be the top-k from initial retrieval
        doc_texts = list(self.documents.values())[:top_k]
        
        return query_text, doc_texts
    
    def save_results(self, results: Dict, output_path: str):
        """Save results to file"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
