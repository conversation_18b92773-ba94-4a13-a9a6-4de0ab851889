import numpy as np
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict


def calculate_dcg(relevances: List[int], k: int = None) -> float:
    """Calculate Discounted Cumulative Gain"""
    if k is not None:
        relevances = relevances[:k]
    
    dcg = 0.0
    for i, rel in enumerate(relevances):
        dcg += (2**rel - 1) / np.log2(i + 2)
    return dcg


def calculate_ndcg(relevances: List[int], ideal_relevances: List[int], k: int = None) -> float:
    """Calculate Normalized Discounted Cumulative Gain"""
    dcg = calculate_dcg(relevances, k)
    idcg = calculate_dcg(sorted(ideal_relevances, reverse=True), k)
    
    if idcg == 0:
        return 0.0
    return dcg / idcg


def calculate_precision_at_k(relevances: List[int], k: int) -> float:
    """Calculate Precision@K"""
    if k == 0:
        return 0.0
    
    relevant_at_k = sum(1 for rel in relevances[:k] if rel > 0)
    return relevant_at_k / k


def calculate_recall_at_k(relevances: List[int], total_relevant: int, k: int) -> float:
    """Calculate Recall@K"""
    if total_relevant == 0:
        return 0.0
    
    relevant_at_k = sum(1 for rel in relevances[:k] if rel > 0)
    return relevant_at_k / total_relevant


def calculate_map(relevances: List[int]) -> float:
    """Calculate Mean Average Precision"""
    if not relevances:
        return 0.0
    
    total_relevant = sum(1 for rel in relevances if rel > 0)
    if total_relevant == 0:
        return 0.0
    
    ap = 0.0
    relevant_count = 0
    
    for i, rel in enumerate(relevances):
        if rel > 0:
            relevant_count += 1
            precision_at_i = relevant_count / (i + 1)
            ap += precision_at_i
    
    return ap / total_relevant


class RankingEvaluator:
    """Evaluator for ranking results"""
    
    def __init__(self):
        self.metrics = {}
    
    def evaluate_query(self, 
                      ranked_doc_ids: List[str], 
                      qrels: Dict[str, int], 
                      k_values: List[int] = [1, 5, 10, 20]) -> Dict[str, float]:
        """
        Evaluate ranking for a single query
        
        Args:
            ranked_doc_ids: List of document IDs in ranked order
            qrels: Relevance judgments {doc_id: relevance_score}
            k_values: List of k values for evaluation
            
        Returns:
            Dictionary of metric values
        """
        # Get relevance scores for ranked documents
        relevances = [qrels.get(doc_id, 0) for doc_id in ranked_doc_ids]
        total_relevant = sum(1 for rel in qrels.values() if rel > 0)
        ideal_relevances = list(qrels.values())
        
        results = {}
        
        # Calculate metrics for different k values
        for k in k_values:
            results[f'NDCG@{k}'] = calculate_ndcg(relevances, ideal_relevances, k)
            results[f'P@{k}'] = calculate_precision_at_k(relevances, k)
            results[f'Recall@{k}'] = calculate_recall_at_k(relevances, total_relevant, k)
        
        # Calculate MAP
        results['MAP'] = calculate_map(relevances)
        
        return results
    
    def evaluate_run(self, 
                    run_results: Dict[str, List[str]], 
                    qrels: Dict[str, Dict[str, int]], 
                    k_values: List[int] = [1, 5, 10, 20]) -> Dict[str, float]:
        """
        Evaluate entire run
        
        Args:
            run_results: {query_id: [ranked_doc_ids]}
            qrels: {query_id: {doc_id: relevance_score}}
            k_values: List of k values for evaluation
            
        Returns:
            Dictionary of averaged metric values
        """
        all_metrics = defaultdict(list)
        
        for query_id, ranked_docs in run_results.items():
            if query_id not in qrels:
                continue
            
            query_metrics = self.evaluate_query(ranked_docs, qrels[query_id], k_values)
            
            for metric, value in query_metrics.items():
                all_metrics[metric].append(value)
        
        # Average metrics across queries
        averaged_metrics = {}
        for metric, values in all_metrics.items():
            averaged_metrics[metric] = np.mean(values) if values else 0.0
        
        return averaged_metrics
    
    def compare_runs(self, 
                    baseline_results: Dict[str, List[str]], 
                    reranked_results: Dict[str, List[str]], 
                    qrels: Dict[str, Dict[str, int]], 
                    k_values: List[int] = [1, 5, 10, 20]) -> Dict[str, Dict[str, float]]:
        """
        Compare baseline and reranked results
        
        Args:
            baseline_results: Baseline ranking results
            reranked_results: Reranked results
            qrels: Relevance judgments
            k_values: List of k values for evaluation
            
        Returns:
            Dictionary with baseline and reranked metrics
        """
        baseline_metrics = self.evaluate_run(baseline_results, qrels, k_values)
        reranked_metrics = self.evaluate_run(reranked_results, qrels, k_values)
        
        return {
            'baseline': baseline_metrics,
            'reranked': reranked_metrics
        }


def plot_comparison(comparison_results: Dict[str, Dict[str, float]], 
                   output_path: str = None, 
                   title: str = "BM25 vs LLM Reranking Comparison"):
    """
    Plot comparison between baseline and reranked results
    
    Args:
        comparison_results: Results from compare_runs
        output_path: Path to save plot
        title: Plot title
    """
    baseline_metrics = comparison_results['baseline']
    reranked_metrics = comparison_results['reranked']
    
    # Prepare data for plotting
    metrics = list(baseline_metrics.keys())
    baseline_values = [baseline_metrics[m] for m in metrics]
    reranked_values = [reranked_metrics[m] for m in metrics]
    
    # Create plot
    fig, ax = plt.subplots(figsize=(12, 6))
    
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, baseline_values, width, label='BM25 Baseline', alpha=0.8)
    bars2 = ax.bar(x + width/2, reranked_values, width, label='LLM Reranked', alpha=0.8)
    
    ax.set_xlabel('Metrics')
    ax.set_ylabel('Score')
    ax.set_title(title)
    ax.set_xticks(x)
    ax.set_xticklabels(metrics, rotation=45)
    ax.legend()
    
    # Add value labels on bars
    def add_value_labels(bars):
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=8)
    
    add_value_labels(bars1)
    add_value_labels(bars2)
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {output_path}")
    
    plt.show()


def print_results_table(comparison_results: Dict[str, Dict[str, float]]):
    """Print results in a formatted table"""
    baseline_metrics = comparison_results['baseline']
    reranked_metrics = comparison_results['reranked']
    
    print("\n" + "="*60)
    print("EVALUATION RESULTS")
    print("="*60)
    print(f"{'Metric':<15} {'BM25 Baseline':<15} {'LLM Reranked':<15} {'Improvement':<15}")
    print("-"*60)
    
    for metric in baseline_metrics:
        baseline_val = baseline_metrics[metric]
        reranked_val = reranked_metrics[metric]
        improvement = ((reranked_val - baseline_val) / baseline_val * 100) if baseline_val > 0 else 0
        
        print(f"{metric:<15} {baseline_val:<15.4f} {reranked_val:<15.4f} {improvement:<15.2f}%")
    
    print("="*60)
