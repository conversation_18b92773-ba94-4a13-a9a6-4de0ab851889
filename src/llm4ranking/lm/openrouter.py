import os
import httpx
from typing import Optional, Union, List, Dict

from openai import OpenAI
from llm4ranking.lm.base import LM, LMOuput


class OpenRouterClient(LM):
    """OpenRouter API client with proxy support"""
    
    def __init__(
        self,
        model: str,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        max_length: Optional[int] = None,
        truncation: Optional[bool] = False,
        proxy_config: Optional[Dict] = None,
        **kwargs,
    ):
        super().__init__()
        self.model = model

        # Use OpenRouter API key
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key must be provided either via `api_key` or OPENROUTER_API_KEY environment variable.")

        # Default to OpenRouter base URL
        self.base_url = base_url or os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")

        # Setup HTTP client with proxy if provided
        http_client = None
        if proxy_config:
            from openai import DefaultHttpxClient
            http_client = DefaultHttpxClient(
                proxy=proxy_config.get('http') or proxy_config.get('https'),
                verify=False,  # You might want to make this configurable
                timeout=60.0   # Increase timeout for proxy connections
            )

        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
            http_client=http_client,
            **kwargs
        )

        self._max_length = max_length
        self._truncation = truncation

    @property
    def max_length(self):
        return self._max_length or 4096

    @property
    def max_new_tokens(self):
        return 4096

    def generate(
        self,
        messages: List[Dict[str, str]],
        return_num_tokens: Optional[bool] = False,
        **kwargs
    ) -> Union[str, LMOuput]:

        # Add OpenRouter specific headers if needed
        extra_headers = kwargs.pop("extra_headers", {})

        # Remove parameters not supported by OpenAI API
        unsupported_params = ['do_sample', 'max_new_tokens']
        for param in unsupported_params:
            if param in kwargs:
                if param == 'max_new_tokens':
                    # Convert max_new_tokens to max_completion_tokens
                    kwargs['max_completion_tokens'] = kwargs.pop('max_new_tokens')
                else:
                    kwargs.pop(param)

        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            max_completion_tokens=kwargs.pop("max_completion_tokens", self.max_new_tokens),
            extra_headers=extra_headers,
            **kwargs
        )

        generated_message = response.choices[0].message.content.strip()

        if return_num_tokens:
            num_processed_tokens = response.usage.prompt_tokens if response.usage else 0
            num_generated_tokens = response.usage.completion_tokens if response.usage else 0

            return LMOuput(
                text=generated_message,
                num_processed_tokens=num_processed_tokens,
                num_generated_tokens=num_generated_tokens,
            )

        return generated_message

    def loglikelihood(self, **kwargs):
        raise NotImplementedError("OpenRouter API does not support loglikelihood calculation")

    def logits(self, **kwargs):
        raise NotImplementedError("OpenRouter API does not support logits calculation for now")
