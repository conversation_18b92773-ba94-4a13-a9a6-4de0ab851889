import bm25s
import numpy as np
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm


class BM25Retriever:
    """BM25 retriever for document ranking"""
    
    def __init__(self, k1: float = 0.9, b: float = 0.4, stopwords: str = "en"):
        """
        Initialize BM25 retriever
        
        Args:
            k1: BM25 k1 parameter
            b: BM25 b parameter  
            stopwords: Stopwords language or list
        """
        self.k1 = k1
        self.b = b
        self.stopwords = stopwords
        self.retriever = None
        self.doc_ids = None
        self.documents = None
        self.indexed = False
    
    def index_documents(self, documents: Dict[str, str], show_progress: bool = True):
        """
        Index documents for retrieval
        
        Args:
            documents: Dictionary mapping doc_id to document text
            show_progress: Whether to show progress bar
        """
        print(f"Indexing {len(documents)} documents...")
        
        self.doc_ids, self.documents = zip(*documents.items())
        self.doc_ids, self.documents = list(self.doc_ids), list(self.documents)
        
        # Tokenize documents
        if show_progress:
            print("Tokenizing documents...")
        corpus_tokens = bm25s.tokenize(self.documents, stopwords=self.stopwords)
        
        # Initialize and index
        self.retriever = bm25s.BM25(k1=self.k1, b=self.b)
        if show_progress:
            print("Building BM25 index...")
        self.retriever.index(corpus_tokens)
        
        self.indexed = True
        print(f"Successfully indexed {len(documents)} documents")
    
    def retrieve(self, query: str, k: int = 100) -> Tuple[List[str], List[float]]:
        """
        Retrieve top-k documents for a query
        
        Args:
            query: Search query
            k: Number of documents to retrieve
            
        Returns:
            Tuple of (doc_ids, scores)
        """
        if not self.indexed:
            raise ValueError("Documents must be indexed before retrieval")
        
        # Tokenize query
        query_tokens = bm25s.tokenize(query, stopwords=self.stopwords)
        if len(query_tokens.vocab) == 0:
            query_tokens = bm25s.tokenize("NONE", stopwords=[])
        
        # Retrieve documents
        hits, scores = self.retriever.retrieve(
            query_tokens, 
            corpus=self.doc_ids, 
            k=min(k, len(self.doc_ids))
        )
        
        # Extract results
        retrieved_doc_ids = [hits[0, i] for i in range(len(hits[0]))]
        retrieved_scores = [float(scores[0, i]) for i in range(len(scores[0]))]
        
        return retrieved_doc_ids, retrieved_scores
    
    def batch_retrieve(self, queries: Dict[str, str], k: int = 100, show_progress: bool = True) -> Dict[str, Dict[str, float]]:
        """
        Retrieve documents for multiple queries
        
        Args:
            queries: Dictionary mapping query_id to query text
            k: Number of documents to retrieve per query
            show_progress: Whether to show progress bar
            
        Returns:
            Dictionary mapping query_id to {doc_id: score}
        """
        if not self.indexed:
            raise ValueError("Documents must be indexed before retrieval")
        
        results = {}
        
        iterator = tqdm(queries.items()) if show_progress else queries.items()
        for query_id, query in iterator:
            if show_progress:
                iterator.set_description(f"Retrieving for query {query_id}")
            
            doc_ids, scores = self.retrieve(query, k)
            results[query_id] = {doc_id: score for doc_id, score in zip(doc_ids, scores)}
        
        return results
    
    def get_document_text(self, doc_id: str) -> Optional[str]:
        """Get document text by ID"""
        if not self.indexed:
            return None
        
        try:
            idx = self.doc_ids.index(doc_id)
            return self.documents[idx]
        except ValueError:
            return None
    
    def get_documents_text(self, doc_ids: List[str]) -> List[str]:
        """Get document texts by IDs"""
        return [self.get_document_text(doc_id) or "" for doc_id in doc_ids]
