#!/usr/bin/env python3
"""
改进的 LLM4Ranking 使用示例

这个示例展示了如何更好地使用 LLM4Ranking 进行文档重排，
包括错误处理、配置管理、结果分析等最佳实践。
"""

import os
import json
from typing import List, Dict, Optional, Tuple
from dotenv import load_dotenv
from llm4ranking import Reranker


class ImprovedReranker:
    """改进的重排器类，提供更好的用户体验和错误处理"""
    
    def __init__(
        self,
        reranking_approach: str = "rankgpt",
        model_type: str = "openrouter",  # 推荐使用 openrouter
        model_name: str = "openai/gpt-4-1106-preview",
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        proxy_config: Optional[Dict] = None,
        **kwargs
    ):
        """
        初始化改进的重排器
        
        Args:
            reranking_approach: 重排方法 ("rankgpt", "rel-gen", "query-gen", "prp-heap")
            model_type: 模型类型 ("openrouter", "openai", "hf")
            model_name: 模型名称
            api_key: API 密钥
            base_url: API 基础 URL
            proxy_config: 代理配置
        """
        # 加载环境变量
        load_dotenv()
        
        # 设置默认值
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY") or os.getenv("OPENAI_API_KEY")
        self.base_url = base_url or os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
        
        if not self.api_key:
            raise ValueError("API key is required. Set OPENROUTER_API_KEY or OPENAI_API_KEY in environment or pass api_key parameter.")
        
        # 设置代理配置
        if proxy_config is None and (os.getenv("HTTP_PROXY") or os.getenv("HTTPS_PROXY")):
            proxy_config = {
                "http": os.getenv("HTTP_PROXY"),
                "https": os.getenv("HTTPS_PROXY")
            }
        
        # 创建模型参数
        model_args = {
            "model": model_name,
            "api_key": self.api_key,
            "base_url": self.base_url,
        }
        
        if proxy_config:
            model_args["proxy_config"] = proxy_config
            print(f"🌐 Using proxy: {proxy_config}")
        
        # 设置重排参数（根据方法类型）
        reranking_args, model_fw_args = self._get_optimal_args(reranking_approach)
        
        try:
            # 初始化重排器
            self.reranker = Reranker(
                reranking_approach=reranking_approach,
                model_type=model_type,
                model_name=model_name,
                model_args=model_args,
                reranking_args=reranking_args,
                model_fw_args=model_fw_args
            )
            
            self.approach = reranking_approach
            self.model_name = model_name
            print(f"✅ Reranker initialized: {reranking_approach} with {model_name}")
            
        except Exception as e:
            print(f"❌ Failed to initialize reranker: {e}")
            raise
    
    def _get_optimal_args(self, approach: str) -> Tuple[Dict, Dict]:
        """根据重排方法获取最优参数"""
        
        # 通用的模型前向参数
        model_fw_args = {
            "max_new_tokens": 120,
            "do_sample": False,  # 会被 OpenRouter 客户端自动过滤
        }
        
        # 根据方法设置重排参数
        if approach in ["rankgpt", "first"]:
            # 滑动窗口方法
            reranking_args = {
                "window_size": 20,
                "step": 10,
                "truncate_length": 300,
            }
        elif approach in ["prp-heap", "prp-allpair", "prp-bubble"]:
            # 成对比较方法
            reranking_args = {
                "topk": 20,
                "truncate_length": 300,
            }
        elif approach == "tourrank":
            # 锦标赛方法
            reranking_args = {
                "tuornament_times": 1,
                "truncate_length": 300,
            }
        else:
            # 点式方法 (rel-gen, query-gen, fg-rel-gen)
            reranking_args = {
                "truncate_length": 300,
            }
        
        return reranking_args, model_fw_args
    
    def rerank(
        self,
        query: str,
        candidates: List[str],
        return_indices: bool = True,
        return_scores: bool = False,
        verbose: bool = True
    ) -> Tuple[List[str], Optional[List[int]], Optional[List[float]]]:
        """
        执行文档重排
        
        Args:
            query: 查询文本
            candidates: 候选文档列表
            return_indices: 是否返回重排后的索引
            return_scores: 是否返回重排分数
            verbose: 是否显示详细信息
            
        Returns:
            重排后的文档列表，可选的索引列表和分数列表
        """
        if not candidates:
            print("⚠️  No candidates provided")
            return [], [], []
        
        if verbose:
            print(f"🔄 Reranking {len(candidates)} documents for query: {query[:50]}...")
        
        try:
            # 执行重排
            if return_indices:
                reranked_docs, indices = self.reranker.rerank(
                    query=query,
                    candidates=candidates,
                    return_indices=True
                )
            else:
                reranked_docs = self.reranker.rerank(
                    query=query,
                    candidates=candidates,
                    return_indices=False
                )
                indices = None
            
            # 生成分数（基于排名位置）
            scores = None
            if return_scores:
                scores = [len(candidates) - i for i in range(len(candidates))]
            
            if verbose:
                print(f"✅ Reranking completed. Top document: {reranked_docs[0][:100]}...")
            
            return reranked_docs, indices, scores
            
        except Exception as e:
            print(f"❌ Reranking failed: {e}")
            if verbose:
                print("🔄 Falling back to original order")
            
            # 返回原始顺序
            indices = list(range(len(candidates))) if return_indices else None
            scores = [len(candidates) - i for i in range(len(candidates))] if return_scores else None
            return candidates, indices, scores
    
    def batch_rerank(
        self,
        queries_and_candidates: List[Tuple[str, List[str]]],
        verbose: bool = True
    ) -> List[Tuple[List[str], List[int]]]:
        """
        批量重排多个查询
        
        Args:
            queries_and_candidates: (query, candidates) 元组列表
            verbose: 是否显示进度
            
        Returns:
            重排结果列表
        """
        results = []
        
        for i, (query, candidates) in enumerate(queries_and_candidates):
            if verbose:
                print(f"📝 Processing query {i+1}/{len(queries_and_candidates)}")
            
            reranked_docs, indices, _ = self.rerank(
                query=query,
                candidates=candidates,
                return_indices=True,
                verbose=False
            )
            
            results.append((reranked_docs, indices))
        
        if verbose:
            print(f"✅ Batch reranking completed for {len(queries_and_candidates)} queries")
        
        return results
    
    def compare_with_baseline(
        self,
        query: str,
        candidates: List[str],
        baseline_order: Optional[List[int]] = None,
        top_k: int = 5
    ) -> Dict:
        """
        与基线排序进行比较
        
        Args:
            query: 查询文本
            candidates: 候选文档列表
            baseline_order: 基线排序索引（默认为原始顺序）
            top_k: 显示前 k 个结果
            
        Returns:
            比较结果字典
        """
        if baseline_order is None:
            baseline_order = list(range(len(candidates)))
        
        # 执行重排
        reranked_docs, reranked_indices, _ = self.rerank(
            query=query,
            candidates=candidates,
            return_indices=True,
            verbose=False
        )
        
        # 计算位置变化
        position_changes = []
        for i, doc_idx in enumerate(reranked_indices[:top_k]):
            original_pos = baseline_order.index(doc_idx) if doc_idx in baseline_order else -1
            new_pos = i
            change = original_pos - new_pos  # 正数表示排名提升
            position_changes.append({
                "doc_index": doc_idx,
                "original_position": original_pos,
                "new_position": new_pos,
                "position_change": change,
                "document": candidates[doc_idx][:100] + "..."
            })
        
        return {
            "query": query,
            "total_candidates": len(candidates),
            "reranked_top_k": position_changes,
            "average_position_change": sum(abs(pc["position_change"]) for pc in position_changes) / len(position_changes)
        }


def main():
    """演示改进的重排器使用方法"""
    
    # 示例数据
    query = "how long is life cycle of flea"
    candidates = [
        "A flea can live up to a year, but its general lifespan depends on its living conditions, such as food supply and temperature.",
        "The life cycle of a flea can last anywhere from 20 days to an entire year. It depends on how long the flea remains in the dormant stage.",
        "Flea larvae spin cocoons around themselves in which they move to the last phase of their development.",
        "The flea life cycle has four main stages: egg, larva, pupa, and adult.",
        "Adult fleas can live for several months without eating, as long as they do not emerge from their cocoons."
    ]
    
    try:
        # 1. 基础使用
        print("=" * 60)
        print("🚀 BASIC RERANKING EXAMPLE")
        print("=" * 60)
        
        reranker = ImprovedReranker(
            reranking_approach="rankgpt",
            model_name="openai/gpt-4-1106-preview"
        )
        
        reranked_docs, indices, scores = reranker.rerank(
            query=query,
            candidates=candidates,
            return_indices=True,
            return_scores=True
        )
        
        print("\n📊 Reranking Results:")
        for i, (doc, idx, score) in enumerate(zip(reranked_docs[:3], indices[:3], scores[:3])):
            print(f"  {i+1}. [Original #{idx+1}] Score: {score}")
            print(f"     {doc[:80]}...")
        
        # 2. 比较分析
        print("\n" + "=" * 60)
        print("📈 COMPARISON ANALYSIS")
        print("=" * 60)
        
        comparison = reranker.compare_with_baseline(
            query=query,
            candidates=candidates,
            top_k=3
        )
        
        print(f"\n🔍 Query: {comparison['query']}")
        print(f"📝 Total candidates: {comparison['total_candidates']}")
        print(f"📊 Average position change: {comparison['average_position_change']:.2f}")
        
        print("\n🏆 Top 3 Results:")
        for item in comparison['reranked_top_k']:
            change_emoji = "📈" if item['position_change'] > 0 else "📉" if item['position_change'] < 0 else "➡️"
            print(f"  {change_emoji} Position {item['original_position']+1} → {item['new_position']+1} (change: {item['position_change']:+d})")
            print(f"     {item['document']}")
        
        # 3. 不同方法比较
        print("\n" + "=" * 60)
        print("🔬 DIFFERENT APPROACHES COMPARISON")
        print("=" * 60)
        
        approaches = ["rankgpt", "rel-gen"]
        for approach in approaches:
            try:
                print(f"\n🧪 Testing {approach}...")
                test_reranker = ImprovedReranker(
                    reranking_approach=approach,
                    model_name="openai/gpt-4-1106-preview"
                )
                
                test_docs, test_indices, _ = test_reranker.rerank(
                    query=query,
                    candidates=candidates,
                    verbose=False
                )
                
                print(f"  Top result: {test_docs[0][:60]}...")
                
            except Exception as e:
                print(f"  ❌ Failed: {e}")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        print("\n💡 Make sure to:")
        print("  1. Set OPENROUTER_API_KEY in your .env file")
        print("  2. Install required dependencies: pip install python-dotenv")
        print("  3. Check your internet connection")


if __name__ == "__main__":
    main()
