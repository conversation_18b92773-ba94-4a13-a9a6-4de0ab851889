#!/usr/bin/env python3
"""
Advanced PyTerrier Search Demo

Enhanced search demo with:
- Query expansion
- Faceted search
- Search analytics
- Export functionality
- Batch search processing
"""

import os
import json
import pandas as pd
import time
from collections import defaultdict, Counter
from typing import List, Dict, Optional, Tuple
import csv


def check_pyterrier():
    """Check if PyTerrier is available"""
    try:
        import pyterrier as pt
        return True, pt
    except ImportError:
        return False, None


class AdvancedSearchDemo:
    """Advanced PyTerrier Search Demo with enhanced features"""
    
    def __init__(self, data_dir: str = "advanced_search_data"):
        """Initialize advanced search demo"""
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        self.pt_available, self.pt = check_pyterrier()
        self.index = None
        self.retrievers = {}
        self.documents = []
        self.search_history = []
        
        if self.pt_available and not self.pt.started():
            self.pt.init()
    
    def create_rich_collection(self) -> List[Dict]:
        """Create a rich document collection with diverse content"""
        print("📚 Creating rich document collection...")
        
        documents = [
            # Technology Documents
            {
                "docno": "tech001", "title": "Introduction to Machine Learning",
                "text": "Machine learning is a method of data analysis that automates analytical model building. It is a branch of artificial intelligence based on the idea that systems can learn from data, identify patterns and make decisions with minimal human intervention. Machine learning algorithms build mathematical models based on training data to make predictions or decisions without being explicitly programmed.",
                "category": "Technology", "subcategory": "AI/ML", "author": "Dr. Sarah Chen",
                "date": "2024-01-15", "tags": ["machine learning", "AI", "algorithms", "data science"],
                "difficulty": "Beginner", "reading_time": 5
            },
            {
                "docno": "tech002", "title": "Deep Learning Neural Networks",
                "text": "Deep learning is part of a broader family of machine learning methods based on artificial neural networks with representation learning. Learning can be supervised, semi-supervised or unsupervised. Deep learning architectures such as deep neural networks, deep belief networks, recurrent neural networks and convolutional neural networks have been applied to fields including computer vision, speech recognition, natural language processing.",
                "category": "Technology", "subcategory": "AI/ML", "author": "Prof. Michael Rodriguez", 
                "date": "2024-02-20", "tags": ["deep learning", "neural networks", "CNN", "RNN"],
                "difficulty": "Advanced", "reading_time": 8
            },
            {
                "docno": "tech003", "title": "Cloud Computing Fundamentals",
                "text": "Cloud computing is the delivery of computing services including servers, storage, databases, networking, software, analytics, and intelligence over the Internet to offer faster innovation, flexible resources, and economies of scale. You typically pay only for cloud services you use, helping lower your operating costs, run your infrastructure more efficiently and scale as your business needs change.",
                "category": "Technology", "subcategory": "Cloud", "author": "Alex Thompson",
                "date": "2024-01-28", "tags": ["cloud computing", "AWS", "Azure", "infrastructure"],
                "difficulty": "Intermediate", "reading_time": 6
            },
            
            # Health Documents  
            {
                "docno": "health001", "title": "Benefits of Regular Exercise",
                "text": "Regular physical activity is one of the most important things you can do for your health. Being physically active can improve your brain health, help manage weight, reduce the risk of disease, strengthen bones and muscles, and improve your ability to do everyday activities. Adults who sit less and do any amount of moderate-to-vigorous physical activity gain some health benefits.",
                "category": "Health", "subcategory": "Fitness", "author": "Dr. Emily Johnson",
                "date": "2024-01-10", "tags": ["exercise", "fitness", "health", "wellness"],
                "difficulty": "Beginner", "reading_time": 4
            },
            {
                "docno": "health002", "title": "Nutrition and Balanced Diet",
                "text": "A healthy diet is a diet that maintains or improves overall health. A healthy diet provides the body with essential nutrition: fluid, macronutrients, micronutrients, and adequate food energy. A healthy diet may contain fruits, vegetables, and whole grains, and includes little to no processed food and sweetened beverages. The requirements for a healthy diet can be met from a variety of plant-based and animal-based foods.",
                "category": "Health", "subcategory": "Nutrition", "author": "Nutritionist Lisa Wang",
                "date": "2024-02-05", "tags": ["nutrition", "diet", "healthy eating", "vitamins"],
                "difficulty": "Beginner", "reading_time": 5
            },
            {
                "docno": "health003", "title": "Mental Health and Wellness",
                "text": "Mental health includes our emotional, psychological, and social well-being. It affects how we think, feel, and act. It also helps determine how we handle stress, relate to others, and make healthy choices. Mental health is important at every stage of life, from childhood and adolescence through adulthood. Over the course of your life, if you experience mental health problems, your thinking, mood, and behavior could be affected.",
                "category": "Health", "subcategory": "Mental Health", "author": "Dr. Robert Kim",
                "date": "2024-02-15", "tags": ["mental health", "wellness", "psychology", "stress"],
                "difficulty": "Intermediate", "reading_time": 6
            },
            
            # Science Documents
            {
                "docno": "science001", "title": "Climate Change and Global Impact",
                "text": "Climate change refers to long-term shifts in global or regional climate patterns. Since the mid-20th century, scientists have observed that the majority of climate change is attributed to human activities, particularly fossil fuel burning, which increases heat-trapping greenhouse gas levels in Earth's atmosphere. Climate change is causing sea levels to rise, ice caps to melt, and weather patterns to shift dramatically.",
                "category": "Science", "subcategory": "Environment", "author": "Dr. James Thompson",
                "date": "2024-01-25", "tags": ["climate change", "environment", "global warming", "sustainability"],
                "difficulty": "Intermediate", "reading_time": 7
            },
            {
                "docno": "science002", "title": "Renewable Energy Solutions",
                "text": "Renewable energy is energy from sources that are naturally replenishing but flow-limited; renewable resources are virtually inexhaustible in duration but limited in the amount of energy that is available per unit of time. The major types of renewable energy sources are solar energy, wind energy, hydroelectric energy, geothermal energy, and biomass energy.",
                "category": "Science", "subcategory": "Energy", "author": "Dr. Maria Gonzalez",
                "date": "2024-02-12", "tags": ["renewable energy", "solar", "wind", "sustainability"],
                "difficulty": "Intermediate", "reading_time": 6
            },
            
            # Travel Documents
            {
                "docno": "travel001", "title": "European City Adventures",
                "text": "Europe offers an incredible diversity of experiences, from the romantic canals of Venice and Amsterdam to the historic architecture of Prague and Vienna. Each city tells its own story through cobblestone streets, magnificent cathedrals, world-class museums, and vibrant local cultures. Whether you're interested in art, history, cuisine, or nightlife, European cities provide unforgettable experiences for every type of traveler.",
                "category": "Travel", "subcategory": "Europe", "author": "Travel Writer Alex Kim",
                "date": "2024-01-30", "tags": ["travel", "Europe", "cities", "culture"],
                "difficulty": "Beginner", "reading_time": 5
            },
            {
                "docno": "travel002", "title": "Adventure Travel in South America",
                "text": "South America is a continent of superlatives and a paradise for adventure travelers. From hiking the ancient Inca Trail to Machu Picchu in Peru to exploring the vast Amazon rainforest, from trekking through the dramatic landscapes of Patagonia to experiencing the vibrant street life of cities like Rio de Janeiro and Buenos Aires, South America offers adventures that will challenge and inspire you.",
                "category": "Travel", "subcategory": "Adventure", "author": "Adventure Guide Carlos Silva",
                "date": "2024-02-18", "tags": ["adventure travel", "South America", "hiking", "nature"],
                "difficulty": "Intermediate", "reading_time": 7
            }
        ]
        
        # Save documents
        docs_path = os.path.join(self.data_dir, "rich_documents.jsonl")
        with open(docs_path, 'w', encoding='utf-8') as f:
            for doc in documents:
                f.write(json.dumps(doc, ensure_ascii=False) + '\n')
        
        self.documents = documents
        
        # Create category and tag statistics
        categories = Counter(d['category'] for d in documents)
        subcategories = Counter(d['subcategory'] for d in documents)
        all_tags = [tag for d in documents for tag in d['tags']]
        tag_counts = Counter(all_tags)
        
        print(f"✅ Created {len(documents)} rich documents")
        print(f"📊 Categories: {dict(categories)}")
        print(f"🏷️  Top tags: {dict(tag_counts.most_common(5))}")
        
        return documents
    
    def build_advanced_index(self) -> bool:
        """Build advanced index with metadata"""
        if not self.pt_available:
            return False
        
        index_path = os.path.join(self.data_dir, "advanced_index")
        
        if os.path.exists(index_path) and os.listdir(index_path):
            print(f"✅ Loading existing advanced index")
            self.index = self.pt.IndexFactory.of(index_path)
            return True
        
        print("🔨 Building advanced index with metadata...")
        
        try:
            def doc_iterator():
                for doc in self.documents:
                    # Create searchable text combining multiple fields
                    searchable_text = f"{doc['title']} {doc['text']} {' '.join(doc['tags'])}"
                    
                    yield {
                        'docno': doc['docno'],
                        'text': searchable_text,
                        'title': doc['title'],
                        'category': doc['category'],
                        'subcategory': doc['subcategory'],
                        'author': doc['author'],
                        'date': doc['date'],
                        'tags': ','.join(doc['tags']),
                        'difficulty': doc['difficulty'],
                        'reading_time': str(doc['reading_time'])
                    }
            
            # Build index with rich metadata
            indexer = self.pt.IterDictIndexer(
                index_path,
                verbose=True,
                meta=['title', 'category', 'subcategory', 'author', 'date', 'tags', 'difficulty', 'reading_time']
            )
            
            index_ref = indexer.index(doc_iterator())
            self.index = self.pt.IndexFactory.of(index_ref)
            
            stats = self.index.getCollectionStatistics()
            print(f"✅ Advanced index built!")
            print(f"📊 Documents: {stats.getNumberOfDocuments()}")
            print(f"📊 Terms: {stats.getNumberOfUniqueTerms()}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error building advanced index: {e}")
            return False
    
    def setup_advanced_retrievers(self) -> bool:
        """Setup advanced retrieval models"""
        if not self.index:
            return False
        
        print("🔧 Setting up advanced retrievers...")
        
        try:
            metadata_fields = ['title', 'category', 'subcategory', 'author', 'date', 'tags', 'difficulty', 'reading_time']
            
            # Standard retrievers
            self.retrievers['BM25'] = self.pt.BatchRetrieve(self.index, wmodel="BM25", metadata=metadata_fields)
            self.retrievers['TF_IDF'] = self.pt.BatchRetrieve(self.index, wmodel="TF_IDF", metadata=metadata_fields)
            self.retrievers['DPH'] = self.pt.BatchRetrieve(self.index, wmodel="DPH", metadata=metadata_fields)
            
            # Query expansion with RM3 (if available)
            try:
                rm3 = self.pt.rewrite.RM3(self.index)
                self.retrievers['BM25_RM3'] = self.retrievers['BM25'] >> rm3 >> self.retrievers['BM25']
                print("✅ Added RM3 query expansion")
            except:
                print("⚠️  RM3 query expansion not available")
            
            print(f"✅ Setup {len(self.retrievers)} advanced retrievers")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up retrievers: {e}")
            return False
    
    def faceted_search(self, query: str, filters: Dict = None, model: str = "BM25") -> pd.DataFrame:
        """Perform faceted search with filters"""
        if not self.pt_available:
            return self._fallback_faceted_search(query, filters)
        
        try:
            # Build query with filters
            if filters:
                filter_parts = []
                for field, value in filters.items():
                    if field in ['category', 'subcategory', 'difficulty']:
                        filter_parts.append(f"{field}:{value}")
                
                if filter_parts:
                    # Combine query with filters
                    filtered_query = f"{query} {' '.join(filter_parts)}"
                else:
                    filtered_query = query
            else:
                filtered_query = query
            
            query_df = pd.DataFrame([{'qid': '1', 'query': filtered_query}])
            
            start_time = time.time()
            results = self.retrievers[model].transform(query_df)
            search_time = time.time() - start_time
            
            # Apply post-filtering if needed
            if filters:
                for field, value in filters.items():
                    if field in results.columns:
                        results = results[results[field] == value]
            
            results['search_time'] = search_time
            results['model'] = model
            
            return results
            
        except Exception as e:
            print(f"❌ Faceted search error: {e}")
            return pd.DataFrame()
    
    def _fallback_faceted_search(self, query: str, filters: Dict = None) -> pd.DataFrame:
        """Fallback faceted search"""
        query_terms = query.lower().split()
        results = []
        
        for doc in self.documents:
            # Apply filters first
            if filters:
                skip = False
                for field, value in filters.items():
                    if field in doc and doc[field] != value:
                        skip = True
                        break
                if skip:
                    continue
            
            # Calculate relevance score
            text = f"{doc['title']} {doc['text']} {' '.join(doc['tags'])}".lower()
            score = sum(1 for term in query_terms if term in text)
            
            if score > 0:
                results.append({
                    'qid': '1', 'docno': doc['docno'], 'score': score,
                    'title': doc['title'], 'category': doc['category'],
                    'subcategory': doc['subcategory'], 'author': doc['author'],
                    'date': doc['date'], 'tags': ','.join(doc['tags']),
                    'difficulty': doc['difficulty'], 'reading_time': doc['reading_time'],
                    'model': 'Filtered_Search'
                })
        
        results.sort(key=lambda x: x['score'], reverse=True)
        return pd.DataFrame(results)
    
    def get_search_suggestions(self, partial_query: str) -> List[str]:
        """Get search suggestions based on document content"""
        suggestions = set()
        partial_lower = partial_query.lower()
        
        # Extract suggestions from titles and tags
        for doc in self.documents:
            # From titles
            title_words = doc['title'].lower().split()
            for word in title_words:
                if word.startswith(partial_lower) and len(word) > len(partial_lower):
                    suggestions.add(word)
            
            # From tags
            for tag in doc['tags']:
                if tag.lower().startswith(partial_lower) and len(tag) > len(partial_lower):
                    suggestions.add(tag)
        
        return sorted(list(suggestions))[:5]
    
    def analyze_search_patterns(self) -> Dict:
        """Analyze search patterns from history"""
        if not self.search_history:
            return {}
        
        analysis = {
            'total_searches': len(self.search_history),
            'unique_queries': len(set(h['query'] for h in self.search_history)),
            'popular_models': Counter(h['model'] for h in self.search_history),
            'popular_terms': Counter(),
            'avg_results': 0,
            'categories_searched': Counter()
        }
        
        total_results = 0
        for search in self.search_history:
            # Count terms
            for term in search['query'].lower().split():
                analysis['popular_terms'][term] += 1
            
            # Count results
            total_results += search.get('num_results', 0)
            
            # Count categories from results
            for category in search.get('result_categories', []):
                analysis['categories_searched'][category] += 1
        
        analysis['avg_results'] = total_results / len(self.search_history) if self.search_history else 0
        
        return analysis
    
    def export_results(self, results: pd.DataFrame, filename: str = None):
        """Export search results to CSV"""
        if results.empty:
            print("❌ No results to export")
            return
        
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"search_results_{timestamp}.csv"
        
        filepath = os.path.join(self.data_dir, filename)
        results.to_csv(filepath, index=False)
        print(f"💾 Results exported to: {filepath}")
    
    def interactive_advanced_search(self):
        """Advanced interactive search interface"""
        print("\n🚀 Advanced PyTerrier Search Interface")
        print("=" * 60)
        print("Advanced commands:")
        print("  - 'query' - basic search")
        print("  - 'filter:category:Technology query' - faceted search")
        print("  - 'suggest:partial' - get search suggestions")
        print("  - 'analytics' - view search analytics")
        print("  - 'export' - export last results")
        print("  - 'help' - show help")
        print("  - 'quit' - exit")
        print("=" * 60)
        
        current_model = "BM25"
        last_results = pd.DataFrame()
        
        while True:
            try:
                user_input = input(f"\n🔍 Advanced Search ({current_model})> ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() == 'quit':
                    print("👋 Goodbye!")
                    break
                
                elif user_input.lower() == 'help':
                    print("\n📚 Advanced Help:")
                    print("  Basic search: 'machine learning'")
                    print("  Faceted search: 'filter:category:Technology neural networks'")
                    print("  Multiple filters: 'filter:category:Health,difficulty:Beginner exercise'")
                    print("  Model change: 'model:TF_IDF query'")
                    print("  Suggestions: 'suggest:mach'")
                    continue
                
                elif user_input.lower() == 'analytics':
                    analysis = self.analyze_search_patterns()
                    if analysis:
                        print(f"\n📊 Search Analytics:")
                        print(f"   Total searches: {analysis['total_searches']}")
                        print(f"   Unique queries: {analysis['unique_queries']}")
                        print(f"   Average results: {analysis['avg_results']:.1f}")
                        print(f"   Popular models: {dict(analysis['popular_models'].most_common(3))}")
                        print(f"   Popular terms: {dict(analysis['popular_terms'].most_common(5))}")
                    else:
                        print("📊 No search history available")
                    continue
                
                elif user_input.lower() == 'export':
                    if not last_results.empty:
                        self.export_results(last_results)
                    else:
                        print("❌ No results to export")
                    continue
                
                elif user_input.startswith('suggest:'):
                    partial = user_input.split(':', 1)[1]
                    suggestions = self.get_search_suggestions(partial)
                    if suggestions:
                        print(f"💡 Suggestions: {', '.join(suggestions)}")
                    else:
                        print("💡 No suggestions found")
                    continue
                
                # Handle faceted search
                filters = {}
                query = user_input
                
                if user_input.startswith('filter:'):
                    parts = user_input.split(' ', 1)
                    if len(parts) == 2:
                        filter_part, query = parts
                        filter_specs = filter_part.split(':', 1)[1]
                        
                        for filter_spec in filter_specs.split(','):
                            if ':' in filter_spec:
                                field, value = filter_spec.split(':', 1)
                                filters[field] = value
                
                # Handle model change
                if query.startswith('model:'):
                    parts = query.split(' ', 1)
                    if len(parts) == 2:
                        model_part, query = parts
                        new_model = model_part.split(':', 1)[1]
                        if new_model in self.retrievers:
                            current_model = new_model
                        else:
                            print(f"❌ Unknown model: {new_model}")
                            continue
                
                # Perform search
                print(f"🔍 Searching: '{query}'" + (f" with filters: {filters}" if filters else ""))
                
                if filters:
                    results = self.faceted_search(query, filters, current_model)
                else:
                    results = self.faceted_search(query, model=current_model)
                
                # Record search
                result_categories = list(results['category'].unique()) if 'category' in results.columns else []
                self.search_history.append({
                    'query': query,
                    'model': current_model,
                    'filters': filters,
                    'num_results': len(results),
                    'result_categories': result_categories,
                    'timestamp': time.time()
                })
                
                # Display results
                self.display_advanced_results(results, query, filters)
                last_results = results
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def display_advanced_results(self, results: pd.DataFrame, query: str, filters: Dict = None):
        """Display advanced search results"""
        if results.empty:
            print("❌ No results found")
            return
        
        print(f"\n🔍 Advanced Search Results")
        print("=" * 80)
        
        # Search metadata
        if 'search_time' in results.columns:
            search_time = results['search_time'].iloc[0]
            model = results['model'].iloc[0]
            print(f"📊 Query: '{query}'" + (f" | Filters: {filters}" if filters else ""))
            print(f"📊 Found {len(results)} results in {search_time:.3f}s using {model}")
        
        # Category breakdown
        if 'category' in results.columns:
            categories = results['category'].value_counts()
            print(f"📂 Categories: {dict(categories)}")
        
        print("-" * 80)
        
        # Display results
        for idx, (_, row) in enumerate(results.head(5).iterrows(), 1):
            print(f"{idx}. [{row.get('docno', 'N/A')}] {row.get('title', 'No Title')}")
            print(f"   📂 {row.get('category', 'N/A')} > {row.get('subcategory', 'N/A')}")
            print(f"   ✍️  {row.get('author', 'N/A')} | 📅 {row.get('date', 'N/A')}")
            print(f"   🏷️  {row.get('tags', 'N/A')}")
            print(f"   📊 Difficulty: {row.get('difficulty', 'N/A')} | ⏱️  {row.get('reading_time', 'N/A')} min")
            print(f"   ⭐ Score: {row.get('score', 0):.4f}")
            print()
    
    def run_complete_demo(self):
        """Run complete advanced demo"""
        print("🚀 Advanced PyTerrier Search Demo")
        print("=" * 50)
        
        # Create rich collection
        self.create_rich_collection()
        
        # Build advanced index
        if self.pt_available:
            if not self.build_advanced_index():
                print("❌ Failed to build index")
                return
            
            if not self.setup_advanced_retrievers():
                print("❌ Failed to setup retrievers")
                return
        else:
            print("⚠️  PyTerrier not available - using enhanced fallback")
        
        # Demo searches
        print("\n🎯 Running Advanced Demo Searches")
        demo_queries = [
            ("machine learning", {"category": "Technology"}),
            ("health", {"difficulty": "Beginner"}),
            ("travel adventure", {"subcategory": "Adventure"})
        ]
        
        for query, filters in demo_queries:
            print(f"\n📝 Demo: '{query}' with filters {filters}")
            results = self.faceted_search(query, filters)
            self.display_advanced_results(results, query, filters)
        
        # Interactive search
        choice = input("\nStart advanced interactive search? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            self.interactive_advanced_search()
        
        print("\n✅ Advanced demo completed!")


def main():
    """Main function"""
    demo = AdvancedSearchDemo()
    demo.run_complete_demo()


if __name__ == "__main__":
    main()
