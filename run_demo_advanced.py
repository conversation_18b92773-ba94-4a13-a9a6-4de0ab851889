#!/usr/bin/env python3
"""
高级 DEMO 运行脚本

这个脚本提供了更多的配置选项来测试不同的重排方法和参数
"""

import argparse
import os
import sys
from demo_dl19_bm25_llm_rerank import DL19Demo


def check_environment():
    """检查环境配置"""
    print("Checking environment...")
    
    # 检查 .env 文件
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please copy .env.example to .env and fill in your OpenRouter API key")
        return False
    
    # 检查 API key
    from config import Config
    if not Config.OPENROUTER_API_KEY:
        print("❌ OPENROUTER_API_KEY not found in .env file!")
        print("Please add your OpenRouter API key to the .env file")
        return False
    
    # 检查 toy_data 文件
    if not os.path.exists('toy_data/dl19_bm25_top20.jsonl'):
        print("❌ toy_data/dl19_bm25_top20.jsonl not found!")
        print("Please make sure the toy data file exists")
        return False
    
    print("✅ Environment check passed!")
    return True


class AdvancedDL19Demo(DL19Demo):
    """高级演示类，支持更多配置选项"""
    
    def __init__(self, reranking_approach="rankgpt", model_name=None, 
                 window_size=20, step=10, truncate_length=300):
        """
        初始化高级演示
        
        Args:
            reranking_approach: 重排方法
            model_name: 模型名称
            window_size: 窗口大小
            step: 步长
            truncate_length: 截断长度
        """
        # 先调用父类的部分初始化
        from config import Config
        Config.validate_config()
        
        from src.llm4ranking.datasets import DL19Dataset
        from src.llm4ranking.evaluation.metrics import RankingEvaluator
        
        self.dataset = DL19Dataset()
        self.evaluator = RankingEvaluator()
        
        # 加载数据
        print("Loading DL19 dataset...")
        self.dataset.load_from_toy_data()
        
        # 获取数据
        self.queries = self.dataset.get_queries()
        self.documents = self.dataset.get_documents()
        
        print(f"Loaded {len(self.queries)} queries and {len(self.documents)} documents")
        
        # 使用自定义参数初始化重排器
        self.reranker = None
        self._init_custom_reranker(reranking_approach, model_name, 
                                 window_size, step, truncate_length)
    
    def _init_custom_reranker(self, reranking_approach, model_name, 
                            window_size, step, truncate_length):
        """使用自定义参数初始化重排器"""
        from config import Config
        from src.llm4ranking import Reranker
        
        print(f"Initializing LLM reranker with approach: {reranking_approach}")
        
        # 获取代理配置
        proxy_config = Config.get_proxy_config()
        
        # 使用指定的模型或默认模型
        model = model_name or Config.DEFAULT_MODEL
        
        # 创建模型参数
        model_args = {
            "model": model,
            "api_key": Config.OPENROUTER_API_KEY,
            "base_url": Config.OPENROUTER_BASE_URL,
        }
        
        if proxy_config:
            model_args["proxy_config"] = proxy_config
            print(f"Using proxy: {proxy_config}")
        
        # OpenRouter 客户端会自动过滤不支持的参数
        model_fw_args = {
            "max_new_tokens": 120,
            "do_sample": False,
        }
        
        # 重排参数 - 根据不同方法设置不同参数
        if reranking_approach in ["rankgpt", "first"]:
            # 滑动窗口方法
            reranking_args = {
                "window_size": window_size,
                "step": step,
                "truncate_length": truncate_length,
            }
        elif reranking_approach in ["prp-heap", "prp-allpair", "prp-bubble"]:
            # 成对比较方法
            reranking_args = {
                "topk": min(window_size, 20),  # 使用 window_size 作为 topk
                "truncate_length": truncate_length,
            }
        else:
            # 点式方法 (rel-gen, query-gen, fg-rel-gen)
            reranking_args = {
                "truncate_length": truncate_length,
            }
        
        self.reranker = Reranker(
            reranking_approach=reranking_approach,
            model_type="openrouter",
            model_name=model,
            model_args=model_args,
            reranking_args=reranking_args,
            model_fw_args=model_fw_args
        )
        
        print(f"Reranker initialized with:")
        print(f"  - Approach: {reranking_approach}")
        print(f"  - Model: {model}")
        print(f"  - Window size: {window_size}")
        print(f"  - Step: {step}")
        print(f"  - Truncate length: {truncate_length}")


def main():
    parser = argparse.ArgumentParser(description='Run Advanced DL19 BM25 + LLM Reranking Demo')
    parser.add_argument('--queries', type=int, default=3, 
                       help='Number of queries to process (default: 3)')
    parser.add_argument('--no-save', action='store_true', 
                       help='Do not save results to files')
    parser.add_argument('--model', type=str, 
                       help='Override model name (e.g., openai/gpt-4-1106-preview)')
    parser.add_argument('--approach', type=str, default='rankgpt',
                       choices=['rankgpt', 'rel-gen', 'query-gen', 'prp-heap', 'prp-allpair'],
                       help='Reranking approach (default: rankgpt)')
    parser.add_argument('--window-size', type=int, default=20,
                       help='Window size for sliding window approaches (default: 20)')
    parser.add_argument('--step', type=int, default=10,
                       help='Step size for sliding window approaches (default: 10)')
    parser.add_argument('--truncate-length', type=int, default=300,
                       help='Document truncation length (default: 300)')
    
    args = parser.parse_args()
    
    # 检查环境
    if not check_environment():
        return 1
    
    # 设置模型（如果指定）
    if args.model:
        os.environ['DEFAULT_MODEL'] = args.model
        print(f"Using model: {args.model}")
    
    try:
        print("\n🚀 Starting Advanced DL19 BM25 + LLM Reranking Demo...")
        print(f"Processing {args.queries} queries")
        print(f"Reranking approach: {args.approach}")
        print(f"Window size: {args.window_size}, Step: {args.step}")
        print(f"Truncate length: {args.truncate_length}")
        print(f"Save results: {not args.no_save}")
        
        # 运行演示
        demo = AdvancedDL19Demo(
            reranking_approach=args.approach,
            model_name=args.model,
            window_size=args.window_size,
            step=args.step,
            truncate_length=args.truncate_length
        )
        demo.run_demo(
            limit_queries=args.queries,
            save_results=not args.no_save
        )
        
        print("\n✅ Demo completed successfully!")
        
        if not args.no_save:
            print("\n📁 Results saved to:")
            print("  - results/bm25_results.json")
            print("  - results/reranked_results.json") 
            print("  - results/evaluation_results.json")
        
        print("\n💡 Tips for better results:")
        print("1. Try different reranking approaches: --approach rel-gen")
        print("2. Adjust window size: --window-size 10")
        print("3. Use different models: --model anthropic/claude-3-sonnet")
        print("4. Increase truncate length: --truncate-length 500")
        
    except KeyboardInterrupt:
        print("\n❌ Demo interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error running demo: {e}")
        print("\nTroubleshooting:")
        print("1. Check your .env file configuration")
        print("2. Verify your OpenRouter API key is valid")
        print("3. Check your internet connection and proxy settings")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
