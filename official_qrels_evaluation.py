#!/usr/bin/env python3
"""
使用官方 TREC DL 2019 qrels 进行公平评估

这个脚本下载并使用官方的 TREC Deep Learning 2019 qrels 来评估
BM25 和 LLM 重排的性能，避免评估偏差问题。
"""

import json
import os
import requests
from typing import Dict, List, Tuple
from src.llm4ranking.datasets import DL19Dataset
from src.llm4ranking.evaluation.metrics import RankingEvaluator, print_results_table


class OfficialQrelsEvaluator:
    """使用官方 qrels 的评估器"""
    
    def __init__(self):
        self.dataset = DL19Dataset()
        self.evaluator = RankingEvaluator()
        self.official_qrels = None
        self.dummy_qrels = None
    
    def download_official_qrels(self, force_download: bool = False) -> bool:
        """
        下载官方 TREC DL 2019 qrels
        
        Args:
            force_download: 是否强制重新下载
            
        Returns:
            是否成功下载/加载
        """
        qrels_path = "data/2019qrels-docs.txt"
        
        # 如果文件已存在且不强制下载，直接加载
        if os.path.exists(qrels_path) and not force_download:
            print(f"✅ Official qrels file already exists: {qrels_path}")
            return self._load_qrels_file(qrels_path)
        
        print("🔄 Downloading official TREC DL 2019 qrels...")
        
        try:
            # 创建数据目录
            os.makedirs("data", exist_ok=True)
            
            # 下载官方 qrels
            url = "https://trec.nist.gov/data/deep/2019qrels-docs.txt"
            
            print(f"📥 Downloading from: {url}")
            response = requests.get(url, timeout=60)
            response.raise_for_status()
            
            # 保存文件
            with open(qrels_path, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"✅ Downloaded official qrels to: {qrels_path}")
            print(f"📊 File size: {len(response.text)} characters")
            
            return self._load_qrels_file(qrels_path)
            
        except requests.RequestException as e:
            print(f"❌ Failed to download qrels: {e}")
            print("💡 You can manually download from: https://trec.nist.gov/data/deep/2019qrels-docs.txt")
            return False
        except Exception as e:
            print(f"❌ Error saving qrels: {e}")
            return False
    
    def _load_qrels_file(self, qrels_path: str) -> bool:
        """加载 qrels 文件"""
        try:
            self.official_qrels = {}
            
            with open(qrels_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📖 Processing {len(lines)} lines from qrels file...")
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                parts = line.split()
                if len(parts) < 4:
                    print(f"⚠️  Skipping malformed line {line_num}: {line}")
                    continue
                
                qid, _, docid, rel = parts[0], parts[1], parts[2], int(parts[3])
                
                if qid not in self.official_qrels:
                    self.official_qrels[qid] = {}
                
                self.official_qrels[qid][docid] = rel
            
            print(f"✅ Loaded official qrels for {len(self.official_qrels)} queries")
            
            # 显示统计信息
            total_judgments = sum(len(docs) for docs in self.official_qrels.values())
            relevant_judgments = sum(
                sum(1 for rel in docs.values() if rel > 0) 
                for docs in self.official_qrels.values()
            )
            
            print(f"📊 Total judgments: {total_judgments}")
            print(f"📊 Relevant judgments: {relevant_judgments}")
            print(f"📊 Relevance rate: {relevant_judgments/total_judgments*100:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading qrels file: {e}")
            return False
    
    def load_demo_results(self) -> Tuple[Dict, Dict]:
        """加载演示结果"""
        try:
            with open("results/bm25_results.json", 'r', encoding='utf-8') as f:
                bm25_data = json.load(f)
            
            with open("results/reranked_results.json", 'r', encoding='utf-8') as f:
                llm_data = json.load(f)
            
            print(f"✅ Loaded demo results for {len(bm25_data)} queries")
            return bm25_data, llm_data
            
        except FileNotFoundError:
            print("❌ Demo results not found. Please run the demo first:")
            print("   python run_demo.py --queries 5")
            return {}, {}
    
    def prepare_evaluation_data(self, bm25_data: Dict, llm_data: Dict) -> Tuple[Dict, Dict, Dict, Dict]:
        """准备评估数据"""
        
        # 转换为排序格式
        bm25_rankings = {qid: [doc['doc_id'] for doc in docs] for qid, docs in bm25_data.items()}
        llm_rankings = {qid: [doc['doc_id'] for doc in docs] for qid, docs in llm_data.items()}
        
        # 加载数据集以获取 dummy qrels
        self.dataset.load_from_toy_data()
        self.dummy_qrels = self.dataset._create_dummy_qrels()
        
        # 找到有官方 qrels 的查询
        if self.official_qrels:
            official_queries = set(self.official_qrels.keys())
            demo_queries = set(bm25_rankings.keys())
            common_queries = official_queries & demo_queries
            
            print(f"📊 Query overlap analysis:")
            print(f"   Official qrels queries: {len(official_queries)}")
            print(f"   Demo queries: {len(demo_queries)}")
            print(f"   Common queries: {len(common_queries)}")
            
            if common_queries:
                print(f"✅ Found {len(common_queries)} queries with official qrels")
                print(f"   Query IDs: {sorted(list(common_queries))[:5]}{'...' if len(common_queries) > 5 else ''}")
            else:
                print("⚠️  No overlap between demo queries and official qrels")
                print("   This might be due to different query ID formats")
        
        return bm25_rankings, llm_rankings, self.official_qrels or {}, self.dummy_qrels
    
    def evaluate_with_official_qrels(self, bm25_rankings: Dict, llm_rankings: Dict, official_qrels: Dict) -> Dict:
        """使用官方 qrels 评估"""
        
        # 找到有官方 qrels 的查询
        common_queries = set(bm25_rankings.keys()) & set(llm_rankings.keys()) & set(official_qrels.keys())
        
        if not common_queries:
            print("❌ No queries found with official qrels")
            return {}
        
        print(f"🔍 Evaluating {len(common_queries)} queries with official qrels")
        
        # 过滤数据
        filtered_bm25 = {q: bm25_rankings[q] for q in common_queries}
        filtered_llm = {q: llm_rankings[q] for q in common_queries}
        filtered_qrels = {q: official_qrels[q] for q in common_queries}
        
        # 执行评估
        bm25_metrics = self.evaluator.evaluate_run(filtered_bm25, filtered_qrels, [1, 5, 10, 20])
        llm_metrics = self.evaluator.evaluate_run(filtered_llm, filtered_qrels, [1, 5, 10, 20])
        
        return {
            'baseline': bm25_metrics,
            'reranked': llm_metrics,
            'num_queries': len(common_queries),
            'query_ids': sorted(list(common_queries))
        }
    
    def evaluate_with_dummy_qrels(self, bm25_rankings: Dict, llm_rankings: Dict, dummy_qrels: Dict) -> Dict:
        """使用 dummy qrels 评估（对比）"""
        
        common_queries = set(bm25_rankings.keys()) & set(llm_rankings.keys()) & set(dummy_qrels.keys())
        
        if not common_queries:
            return {}
        
        print(f"🔍 Evaluating {len(common_queries)} queries with dummy qrels (for comparison)")
        
        # 过滤数据
        filtered_bm25 = {q: bm25_rankings[q] for q in common_queries}
        filtered_llm = {q: llm_rankings[q] for q in common_queries}
        filtered_qrels = {q: dummy_qrels[q] for q in common_queries}
        
        # 执行评估
        bm25_metrics = self.evaluator.evaluate_run(filtered_bm25, filtered_qrels, [1, 5, 10, 20])
        llm_metrics = self.evaluator.evaluate_run(filtered_llm, filtered_qrels, [1, 5, 10, 20])
        
        return {
            'baseline': bm25_metrics,
            'reranked': llm_metrics,
            'num_queries': len(common_queries)
        }
    
    def run_comprehensive_evaluation(self):
        """运行综合评估"""
        
        print("🚀 Official TREC DL 2019 Qrels Evaluation")
        print("=" * 60)
        
        # 1. 下载官方 qrels
        if not self.download_official_qrels():
            print("❌ Cannot proceed without official qrels")
            return
        
        # 2. 加载演示结果
        bm25_data, llm_data = self.load_demo_results()
        if not bm25_data or not llm_data:
            return
        
        # 3. 准备评估数据
        bm25_rankings, llm_rankings, official_qrels, dummy_qrels = self.prepare_evaluation_data(bm25_data, llm_data)
        
        # 4. 使用官方 qrels 评估
        print("\n" + "=" * 60)
        print("📊 EVALUATION WITH OFFICIAL QRELS")
        print("=" * 60)
        
        official_results = self.evaluate_with_official_qrels(bm25_rankings, llm_rankings, official_qrels)
        
        if official_results:
            print(f"\n✅ Official Qrels Evaluation ({official_results['num_queries']} queries):")
            print_results_table(official_results)
            
            # 保存官方评估结果
            with open("results/official_qrels_evaluation.json", 'w', encoding='utf-8') as f:
                json.dump(official_results, f, indent=2, ensure_ascii=False)
            print("💾 Official evaluation results saved to: results/official_qrels_evaluation.json")
        
        # 5. 对比 dummy qrels 评估
        print("\n" + "=" * 60)
        print("📊 COMPARISON WITH DUMMY QRELS")
        print("=" * 60)
        
        dummy_results = self.evaluate_with_dummy_qrels(bm25_rankings, llm_rankings, dummy_qrels)
        
        if dummy_results:
            print(f"\n⚠️  Dummy Qrels Evaluation ({dummy_results['num_queries']} queries):")
            print_results_table(dummy_results)
        
        # 6. 分析和总结
        self.analyze_results(official_results, dummy_results)
    
    def analyze_results(self, official_results: Dict, dummy_results: Dict):
        """分析评估结果"""
        
        print("\n" + "=" * 60)
        print("🎯 ANALYSIS AND INSIGHTS")
        print("=" * 60)
        
        if not official_results:
            print("❌ No official qrels evaluation available")
            print("\n💡 Possible reasons:")
            print("   1. Query ID format mismatch between demo data and official qrels")
            print("   2. Demo queries not included in TREC DL 2019 official evaluation")
            print("   3. Network issues preventing qrels download")
            
            print("\n🔧 Recommendations:")
            print("   1. Check query ID mapping in toy_data/dl19_bm25_top20.jsonl")
            print("   2. Verify that demo queries are from TREC DL 2019 dataset")
            print("   3. Consider using a subset of official TREC DL 2019 queries")
            return
        
        print("✅ Successfully evaluated with official qrels!")
        
        # 比较官方 vs dummy qrels 结果
        if dummy_results:
            print("\n📊 Comparison: Official vs Dummy Qrels")
            print("-" * 40)
            
            for metric in ['NDCG@5', 'NDCG@10', 'MAP']:
                official_bm25 = official_results['baseline'].get(metric, 0)
                official_llm = official_results['reranked'].get(metric, 0)
                dummy_bm25 = dummy_results['baseline'].get(metric, 0)
                dummy_llm = dummy_results['reranked'].get(metric, 0)
                
                official_improvement = ((official_llm - official_bm25) / official_bm25 * 100) if official_bm25 > 0 else 0
                dummy_improvement = ((dummy_llm - dummy_bm25) / dummy_bm25 * 100) if dummy_bm25 > 0 else 0
                
                print(f"{metric}:")
                print(f"  Official qrels: {official_improvement:+.1f}% improvement")
                print(f"  Dummy qrels:    {dummy_improvement:+.1f}% improvement")
        
        print("\n💡 Key Insights:")
        
        # 分析 LLM 在官方评估下的表现
        official_improvement = 0
        for metric in ['NDCG@5', 'NDCG@10', 'MAP']:
            bm25_val = official_results['baseline'].get(metric, 0)
            llm_val = official_results['reranked'].get(metric, 0)
            if bm25_val > 0:
                improvement = (llm_val - bm25_val) / bm25_val * 100
                official_improvement += improvement
        
        official_improvement /= 3  # 平均改进
        
        if official_improvement > 5:
            print("   📈 LLM reranking shows significant improvement with official qrels")
        elif official_improvement > 0:
            print("   📊 LLM reranking shows modest improvement with official qrels")
        elif official_improvement > -5:
            print("   ➡️  LLM reranking shows comparable performance with official qrels")
        else:
            print("   📉 LLM reranking shows degradation even with official qrels")
        
        print("   ✅ This evaluation is more reliable than dummy qrels-based evaluation")
        print("   🎯 Official qrels provide unbiased ground truth for comparison")


def main():
    """主函数"""
    evaluator = OfficialQrelsEvaluator()
    evaluator.run_comprehensive_evaluation()


if __name__ == "__main__":
    main()
