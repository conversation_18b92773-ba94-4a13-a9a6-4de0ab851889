#!/usr/bin/env python3
"""
PyTerrier 简化演示

一个简化的 PyTerrier DL19 演示，专注于核心功能展示
适合快速测试和学习 PyTerrier 基础用法
"""

import os
import pandas as pd
import json
from typing import List, Dict


def check_pyterrier():
    """检查 PyTerrier 是否可用"""
    try:
        import pyterrier as pt
        return True, pt
    except ImportError:
        return False, None


class SimplePyTerrierDemo:
    """简化的 PyTerrier 演示"""
    
    def __init__(self):
        self.pt_available, self.pt = check_pyterrier()
        self.data_dir = "simple_demo_data"
        os.makedirs(self.data_dir, exist_ok=True)
    
    def create_sample_data(self):
        """创建示例数据"""
        print("📝 Creating sample data...")
        
        # 示例文档
        documents = [
            {
                "docno": "doc1",
                "text": "<PERSON> was an American merchant sea captain who is known for his achievements in connection with two trading voyages to the northern Pacific coast of North America, between 1790 and 1793."
            },
            {
                "docno": "doc2", 
                "text": "The cost of interior concrete flooring varies depending on the complexity of the job and your location. Basic concrete floors can cost $2-6 per square foot, while decorative concrete can cost $6-15 per square foot."
            },
            {
                "docno": "doc3",
                "text": "The United States entered World War I in April 1917 due to several factors including Germany's resumption of unrestricted submarine warfare and the Zimmermann Telegram incident."
            },
            {
                "docno": "doc4",
                "text": "The Commonwealth of Independent States (CIS) was formed on December 8, 1991, by the leaders of Russia, Ukraine, and Belarus following the dissolution of the Soviet Union."
            },
            {
                "docno": "doc5",
                "text": "Visceral refers to something felt in or as if in the internal organs of the body. It relates to deep inward feelings rather than to the intellect."
            },
            {
                "docno": "doc6",
                "text": "WiFi and Bluetooth are both wireless communication technologies. WiFi typically has a longer range and higher data transfer rates, while Bluetooth is designed for short-range device connections."
            },
            {
                "docno": "doc7",
                "text": "Daily life in Thailand varies between urban and rural areas. Thai people typically start their day early, with many visiting temples, enjoying street food, and maintaining strong family connections."
            },
            {
                "docno": "doc8",
                "text": "Spruce trees are evergreen conifers with needle-like leaves. They typically have a conical shape, with branches arranged in whorls around the trunk."
            },
            {
                "docno": "doc9",
                "text": "Famvir (famciclovir) is an antiviral medication prescribed for treating herpes simplex virus infections, including cold sores and genital herpes."
            },
            {
                "docno": "doc10",
                "text": "Durable medical equipment (DME) consists of reusable medical devices such as wheelchairs, hospital beds, oxygen equipment, and mobility aids prescribed by healthcare providers."
            }
        ]
        
        # 示例查询
        queries = [
            {"qid": "1", "query": "who is robert gray"},
            {"qid": "2", "query": "cost of interior concrete flooring"},
            {"qid": "3", "query": "why did the us enter ww1"},
            {"qid": "4", "query": "who formed the commonwealth of independent states"},
            {"qid": "5", "query": "define visceral"},
            {"qid": "6", "query": "what is wifi vs bluetooth"},
            {"qid": "7", "query": "daily life of thai people"},
            {"qid": "8", "query": "physical description of spruce"},
            {"qid": "9", "query": "what is famvir prescribed for"},
            {"qid": "10", "query": "what is durable medical equipment"}
        ]
        
        # 保存数据
        docs_path = os.path.join(self.data_dir, "documents.jsonl")
        with open(docs_path, 'w', encoding='utf-8') as f:
            for doc in documents:
                f.write(json.dumps(doc) + '\n')
        
        queries_path = os.path.join(self.data_dir, "queries.csv")
        queries_df = pd.DataFrame(queries)
        queries_df.to_csv(queries_path, index=False)
        
        print(f"✅ Created {len(documents)} documents and {len(queries)} queries")
        return docs_path, queries_path, documents, queries
    
    def run_with_pyterrier(self, docs_path: str, queries_path: str):
        """使用 PyTerrier 运行演示"""
        print("\n🚀 Running PyTerrier Demo")
        print("=" * 40)
        
        # 初始化 PyTerrier
        if not self.pt.started():
            self.pt.init()
            print("✅ PyTerrier initialized")
        
        # 读取数据
        def doc_iterator():
            with open(docs_path, 'r', encoding='utf-8') as f:
                for line in f:
                    doc = json.loads(line.strip())
                    yield doc
        
        queries_df = pd.read_csv(queries_path)
        print(f"📖 Loaded {len(queries_df)} queries")
        
        # 构建索引
        index_path = os.path.join(self.data_dir, "index")
        print("🔨 Building index...")
        
        indexer = self.pt.IterDictIndexer(index_path, verbose=True)
        index_ref = indexer.index(doc_iterator())
        index = self.pt.IndexFactory.of(index_ref)
        
        print("✅ Index built successfully")
        print(f"📊 Index statistics:")
        print(f"   Documents: {index.getCollectionStatistics().getNumberOfDocuments()}")
        print(f"   Terms: {index.getCollectionStatistics().getNumberOfUniqueTerms()}")
        
        # 创建 BM25 检索器
        bm25 = self.pt.BatchRetrieve(index, wmodel="BM25")
        print("✅ BM25 retriever created")
        
        # 执行检索
        print("\n🔍 Running retrieval...")
        results = bm25.transform(queries_df)
        
        print(f"✅ Retrieved {len(results)} results")
        print(f"📊 Average results per query: {len(results) / len(queries_df):.1f}")
        
        # 显示示例结果
        self.show_results(results, queries_df)
        
        # 保存结果
        results_path = os.path.join(self.data_dir, "pyterrier_results.csv")
        results.to_csv(results_path, index=False)
        print(f"\n💾 Results saved to: {results_path}")
        
        return results
    
    def run_without_pyterrier(self, documents: List[Dict], queries: List[Dict]):
        """不使用 PyTerrier 的简单演示"""
        print("\n📚 PyTerrier Not Available - Showing Conceptual Demo")
        print("=" * 50)
        
        print("🔧 What PyTerrier would do:")
        print("1. 📖 Index documents with Terrier IR platform")
        print("2. 🔍 Execute BM25 retrieval for each query")
        print("3. 📊 Return ranked results with scores")
        print("4. 📈 Provide evaluation metrics")
        
        print(f"\n📝 Sample Data Overview:")
        print(f"   Documents: {len(documents)}")
        print(f"   Queries: {len(queries)}")
        
        print(f"\n🔍 Sample Queries:")
        for i, query in enumerate(queries[:5], 1):
            print(f"   {i}. {query['query']}")
        
        print(f"\n📄 Sample Documents:")
        for i, doc in enumerate(documents[:3], 1):
            text = doc['text'][:80] + "..." if len(doc['text']) > 80 else doc['text']
            print(f"   {i}. [{doc['docno']}] {text}")
        
        # 简单的关键词匹配演示
        print(f"\n🎯 Simple Keyword Matching Demo:")
        sample_query = queries[0]['query']  # "who is robert gray"
        query_terms = sample_query.lower().split()
        
        print(f"Query: '{sample_query}'")
        print(f"Query terms: {query_terms}")
        
        matches = []
        for doc in documents:
            doc_text = doc['text'].lower()
            score = sum(1 for term in query_terms if term in doc_text)
            if score > 0:
                matches.append((doc['docno'], score, doc['text'][:100] + "..."))
        
        matches.sort(key=lambda x: x[1], reverse=True)
        
        print(f"\nTop matches:")
        for i, (docno, score, text) in enumerate(matches[:3], 1):
            print(f"   {i}. [{docno}] Score: {score} - {text}")
    
    def show_results(self, results: pd.DataFrame, queries_df: pd.DataFrame):
        """显示检索结果"""
        print(f"\n🔍 Sample Retrieval Results:")
        print("=" * 60)
        
        # 获取查询映射
        query_map = dict(zip(queries_df['qid'], queries_df['query']))
        
        # 显示前3个查询的结果
        for qid in queries_df['qid'].head(3):
            query_text = query_map[qid]
            query_results = results[results['qid'] == qid].head(3)
            
            print(f"\n📝 Query {qid}: {query_text}")
            print("-" * 40)
            
            if not query_results.empty:
                for idx, (_, row) in enumerate(query_results.iterrows(), 1):
                    score = row.get('score', 0)
                    docno = row.get('docno', 'Unknown')
                    print(f"   {idx}. [{docno}] Score: {score:.4f}")
            else:
                print("   No results found")
    
    def run_demo(self):
        """运行完整演示"""
        print("🎯 PyTerrier DL19 Simple Demo")
        print("=" * 40)
        
        # 创建示例数据
        docs_path, queries_path, documents, queries = self.create_sample_data()
        
        if self.pt_available:
            try:
                # 使用 PyTerrier 运行
                results = self.run_with_pyterrier(docs_path, queries_path)
                
                print("\n🎉 PyTerrier demo completed successfully!")
                print("\n📚 Key PyTerrier Features Demonstrated:")
                print("   • Document indexing with IterDictIndexer")
                print("   • BM25 retrieval with BatchRetrieve") 
                print("   • Pandas DataFrame integration")
                print("   • Automatic scoring and ranking")
                
            except Exception as e:
                print(f"\n❌ PyTerrier demo failed: {e}")
                print("Falling back to conceptual demo...")
                self.run_without_pyterrier(documents, queries)
        else:
            print("\n⚠️  PyTerrier not available")
            print("To install PyTerrier, run: python setup_pyterrier.py")
            self.run_without_pyterrier(documents, queries)
        
        print(f"\n📁 Demo data saved in: {self.data_dir}/")
        print("\n💡 Next steps:")
        print("   1. Install PyTerrier: python setup_pyterrier.py")
        print("   2. Run full demo: python pyterrier_dl19_demo.py")
        print("   3. Explore PyTerrier docs: https://pyterrier.readthedocs.io/")


def main():
    """主函数"""
    demo = SimplePyTerrierDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
