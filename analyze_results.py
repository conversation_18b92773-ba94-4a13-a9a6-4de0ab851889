#!/usr/bin/env python3
"""
结果分析脚本

分析 BM25 vs LLM 重排的结果，提供详细的洞察
"""

import json
import argparse
from typing import Dict, List
import matplotlib.pyplot as plt
import numpy as np


def load_results(results_dir: str = "results"):
    """加载结果文件"""
    try:
        with open(f"{results_dir}/bm25_results.json", 'r', encoding='utf-8') as f:
            bm25_results = json.load(f)
        
        with open(f"{results_dir}/reranked_results.json", 'r', encoding='utf-8') as f:
            reranked_results = json.load(f)
        
        with open(f"{results_dir}/evaluation_results.json", 'r', encoding='utf-8') as f:
            evaluation_results = json.load(f)
        
        return bm25_results, reranked_results, evaluation_results
    
    except FileNotFoundError as e:
        print(f"❌ Results file not found: {e}")
        print("Please run the demo first to generate results")
        return None, None, None


def analyze_ranking_changes(bm25_results: Dict, reranked_results: Dict):
    """分析排名变化"""
    print("\n" + "="*60)
    print("RANKING CHANGE ANALYSIS")
    print("="*60)
    
    total_changes = 0
    total_docs = 0
    position_changes = []
    
    for query_id in bm25_results:
        if query_id not in reranked_results:
            continue
        
        bm25_docs = [doc["doc_id"] for doc in bm25_results[query_id]]
        reranked_docs = [doc["doc_id"] for doc in reranked_results[query_id]]
        
        # 计算位置变化
        for i, doc_id in enumerate(bm25_docs):
            if doc_id in reranked_docs:
                new_pos = reranked_docs.index(doc_id)
                position_change = i - new_pos  # 正数表示排名提升
                position_changes.append(position_change)
                if position_change != 0:
                    total_changes += 1
            total_docs += 1
    
    change_rate = (total_changes / total_docs) * 100 if total_docs > 0 else 0
    avg_change = np.mean(np.abs(position_changes)) if position_changes else 0
    
    print(f"Total documents analyzed: {total_docs}")
    print(f"Documents with position changes: {total_changes}")
    print(f"Change rate: {change_rate:.2f}%")
    print(f"Average absolute position change: {avg_change:.2f}")
    
    # 分析位置变化分布
    if position_changes:
        improvements = [c for c in position_changes if c > 0]
        degradations = [c for c in position_changes if c < 0]
        
        print(f"Documents improved: {len(improvements)} ({len(improvements)/len(position_changes)*100:.1f}%)")
        print(f"Documents degraded: {len(degradations)} ({len(degradations)/len(position_changes)*100:.1f}%)")
        
        if improvements:
            print(f"Average improvement: {np.mean(improvements):.2f} positions")
        if degradations:
            print(f"Average degradation: {np.mean(np.abs(degradations)):.2f} positions")


def analyze_query_performance(evaluation_results: Dict):
    """分析查询级别的性能"""
    print("\n" + "="*60)
    print("PERFORMANCE ANALYSIS")
    print("="*60)
    
    baseline = evaluation_results["baseline"]
    reranked = evaluation_results["reranked"]
    
    print("Metric-wise Analysis:")
    print("-" * 40)
    
    for metric in baseline:
        baseline_val = baseline[metric]
        reranked_val = reranked[metric]
        improvement = ((reranked_val - baseline_val) / baseline_val * 100) if baseline_val > 0 else 0
        
        status = "📈" if improvement > 0 else "📉" if improvement < 0 else "➡️"
        print(f"{status} {metric:<12}: {baseline_val:.4f} → {reranked_val:.4f} ({improvement:+.2f}%)")
    
    # 计算总体趋势
    improvements = []
    for metric in baseline:
        if baseline[metric] > 0:
            improvement = ((reranked[metric] - baseline[metric]) / baseline[metric] * 100)
            improvements.append(improvement)
    
    if improvements:
        avg_improvement = np.mean(improvements)
        print(f"\nOverall average improvement: {avg_improvement:+.2f}%")
        
        if avg_improvement > 0:
            print("✅ LLM reranking shows overall improvement")
        elif avg_improvement < -5:
            print("⚠️  LLM reranking shows significant degradation")
        else:
            print("➡️  LLM reranking shows mixed results")


def suggest_improvements(evaluation_results: Dict):
    """提供改进建议"""
    print("\n" + "="*60)
    print("IMPROVEMENT SUGGESTIONS")
    print("="*60)
    
    baseline = evaluation_results["baseline"]
    reranked = evaluation_results["reranked"]
    
    # 分析哪些指标表现不佳
    poor_metrics = []
    for metric in baseline:
        if baseline[metric] > 0:
            improvement = ((reranked[metric] - baseline[metric]) / baseline[metric] * 100)
            if improvement < -10:  # 下降超过10%
                poor_metrics.append((metric, improvement))
    
    if poor_metrics:
        print("🔧 Areas needing improvement:")
        for metric, improvement in poor_metrics:
            print(f"   - {metric}: {improvement:.1f}% degradation")
        
        print("\n💡 Suggested actions:")
        
        # 基于具体指标给出建议
        if any("NDCG@5" in m[0] or "NDCG@10" in m[0] for m in poor_metrics):
            print("   1. Try smaller window size (--window-size 10)")
            print("   2. Reduce step size (--step 5)")
            print("   3. Try different reranking approach (--approach rel-gen)")
        
        if any("P@" in m[0] for m in poor_metrics):
            print("   4. Increase document truncation length (--truncate-length 500)")
            print("   5. Try a more powerful model (--model openai/gpt-4)")
        
        if any("MAP" in m[0] for m in poor_metrics):
            print("   6. Consider using pairwise approaches (--approach prp-heap)")
    
    else:
        print("✅ No significant performance issues detected")
    
    print("\n🎯 General recommendations:")
    print("   - Use real qrels instead of BM25-based dummy qrels for accurate evaluation")
    print("   - Test with more queries for statistical significance")
    print("   - Try different models to find the best fit")
    print("   - Consider ensemble approaches combining multiple rerankers")


def create_visualization(evaluation_results: Dict, save_path: str = None):
    """创建可视化图表"""
    baseline = evaluation_results["baseline"]
    reranked = evaluation_results["reranked"]
    
    metrics = list(baseline.keys())
    baseline_values = [baseline[m] for m in metrics]
    reranked_values = [reranked[m] for m in metrics]
    
    # 创建对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 左图：绝对值对比
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, baseline_values, width, label='BM25 Baseline', alpha=0.8)
    bars2 = ax1.bar(x + width/2, reranked_values, width, label='LLM Reranked', alpha=0.8)
    
    ax1.set_xlabel('Metrics')
    ax1.set_ylabel('Score')
    ax1.set_title('BM25 vs LLM Reranking Performance')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 右图：改进百分比
    improvements = []
    for i, metric in enumerate(metrics):
        if baseline_values[i] > 0:
            improvement = ((reranked_values[i] - baseline_values[i]) / baseline_values[i] * 100)
            improvements.append(improvement)
        else:
            improvements.append(0)
    
    colors = ['green' if imp > 0 else 'red' if imp < 0 else 'gray' for imp in improvements]
    bars = ax2.bar(metrics, improvements, color=colors, alpha=0.7)
    ax2.set_xlabel('Metrics')
    ax2.set_ylabel('Improvement (%)')
    ax2.set_title('Performance Change (%)')
    ax2.set_xticklabels(metrics, rotation=45)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, imp in zip(bars, improvements):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (1 if height > 0 else -3),
                f'{imp:.1f}%', ha='center', va='bottom' if height > 0 else 'top', fontsize=8)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📊 Visualization saved to {save_path}")
    
    plt.show()


def main():
    parser = argparse.ArgumentParser(description='Analyze BM25 vs LLM reranking results')
    parser.add_argument('--results-dir', type=str, default='results',
                       help='Directory containing result files (default: results)')
    parser.add_argument('--save-plot', type=str,
                       help='Save visualization plot to file')
    parser.add_argument('--no-plot', action='store_true',
                       help='Skip creating visualization')
    
    args = parser.parse_args()
    
    # 加载结果
    bm25_results, reranked_results, evaluation_results = load_results(args.results_dir)
    
    if not all([bm25_results, reranked_results, evaluation_results]):
        return 1
    
    print("🔍 Analyzing BM25 vs LLM Reranking Results")
    print("=" * 60)
    
    # 执行分析
    analyze_ranking_changes(bm25_results, reranked_results)
    analyze_query_performance(evaluation_results)
    suggest_improvements(evaluation_results)
    
    # 创建可视化
    if not args.no_plot:
        try:
            create_visualization(evaluation_results, args.save_plot)
        except ImportError:
            print("\n⚠️  Matplotlib not available, skipping visualization")
        except Exception as e:
            print(f"\n⚠️  Error creating visualization: {e}")
    
    return 0


if __name__ == "__main__":
    exit(main())
