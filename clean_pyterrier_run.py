
import os
import pandas as pd

def run_pyterrier_demo():
    """Run PyTerrier demo in clean environment"""
    try:
        import pyterrier as pt
        
        print("🔄 Initializing PyTerrier in clean environment...")
        
        # Initialize PyTerrier
        pt.init()
        print("✅ PyTerrier initialized successfully!")
        
        # Create sample documents
        documents = [
            {"docno": "doc1", "text": "Machine learning is a subset of artificial intelligence."},
            {"docno": "doc2", "text": "Deep learning uses neural networks with multiple layers."},
            {"docno": "doc3", "text": "Information retrieval finds relevant documents from collections."},
            {"docno": "doc4", "text": "BM25 is a ranking function for search engines."},
            {"docno": "doc5", "text": "PyTerrier is a Python framework for information retrieval."}
        ]
        
        print(f"📚 Created {len(documents)} documents")
        
        # Build index
        print("🔨 Building index...")
        indexer = pt.IterDictIndexer("./clean_index", overwrite=True)
        index_ref = indexer.index(documents)
        index = pt.IndexFactory.of(index_ref)
        
        # Create retriever
        bm25 = pt.BatchRetrieve(index, wmodel="BM25")
        
        # Test search
        query_df = pd.DataFrame([{"qid": "1", "query": "machine learning"}])
        results = bm25.transform(query_df)
        
        print("\n🔍 Search Results for 'machine learning':")
        print("=" * 50)
        
        for idx, (_, row) in enumerate(results.iterrows(), 1):
            print(f"{idx}. [{row['docno']}] Score: {row['score']:.4f}")
        
        print("\n✅ PyTerrier demo completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ PyTerrier demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_pyterrier_demo()
    if not success:
        print("\n💡 Try the alternative demo instead:")
        print("   python alternative_search_demo.py")
