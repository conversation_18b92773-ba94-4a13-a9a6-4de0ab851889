#!/usr/bin/env python3
"""
分析官方 qrels 评估结果

深入分析为什么官方 qrels 评估结果为 0，并提供详细的诊断信息
"""

import json
import os
from typing import Dict, List, Set
from collections import defaultdict


class QrelsAnalyzer:
    """官方 qrels 分析器"""
    
    def __init__(self):
        self.official_qrels = {}
        self.demo_data = {}
        self.load_data()
    
    def load_data(self):
        """加载所有相关数据"""
        
        # 加载官方 qrels
        if os.path.exists("data/2019qrels-docs.txt"):
            self.load_official_qrels()
        else:
            print("❌ Official qrels not found. Run official_qrels_evaluation.py first.")
            return
        
        # 加载演示数据
        if os.path.exists("results/bm25_results.json"):
            with open("results/bm25_results.json", 'r', encoding='utf-8') as f:
                self.demo_data = json.load(f)
        else:
            print("❌ Demo results not found. Run demo first.")
            return
        
        # 加载 toy data 以获取查询文本
        self.toy_data = {}
        if os.path.exists("toy_data/dl19_bm25_top20.jsonl"):
            with open("toy_data/dl19_bm25_top20.jsonl", 'r', encoding='utf-8') as f:
                for line in f:
                    item = json.loads(line)
                    query_id = str(item['hits'][0]['qid'])
                    self.toy_data[query_id] = {
                        'query': item['query'],
                        'hits': item['hits']
                    }
    
    def load_official_qrels(self):
        """加载官方 qrels"""
        with open("data/2019qrels-docs.txt", 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                parts = line.split()
                if len(parts) >= 4:
                    qid, _, docid, rel = parts[0], parts[1], parts[2], int(parts[3])
                    if qid not in self.official_qrels:
                        self.official_qrels[qid] = {}
                    self.official_qrels[qid][docid] = rel
    
    def analyze_query_coverage(self):
        """分析查询覆盖情况"""
        print("🔍 QUERY COVERAGE ANALYSIS")
        print("=" * 50)
        
        demo_queries = set(self.demo_data.keys())
        official_queries = set(self.official_qrels.keys())
        toy_queries = set(self.toy_data.keys())
        
        print(f"📊 Query Statistics:")
        print(f"   Demo queries: {len(demo_queries)}")
        print(f"   Official qrels queries: {len(official_queries)}")
        print(f"   Toy data queries: {len(toy_queries)}")
        
        # 查找重叠
        demo_official_overlap = demo_queries & official_queries
        demo_toy_overlap = demo_queries & toy_queries
        
        print(f"\n🔗 Query Overlaps:")
        print(f"   Demo ∩ Official: {len(demo_official_overlap)}")
        print(f"   Demo ∩ Toy: {len(demo_toy_overlap)}")
        
        if demo_official_overlap:
            print(f"\n✅ Common queries with official qrels:")
            for qid in sorted(list(demo_official_overlap))[:10]:
                query_text = self.toy_data.get(qid, {}).get('query', 'Unknown')
                print(f"   {qid}: {query_text[:60]}...")
        
        return demo_official_overlap
    
    def analyze_document_coverage(self, common_queries: Set[str]):
        """分析文档覆盖情况"""
        print(f"\n🔍 DOCUMENT COVERAGE ANALYSIS")
        print("=" * 50)
        
        total_demo_docs = 0
        total_official_docs = 0
        total_overlaps = 0
        
        coverage_details = []
        
        for query_id in common_queries:
            # 获取演示中的文档
            demo_docs = set()
            if query_id in self.demo_data:
                demo_docs = {doc['doc_id'] for doc in self.demo_data[query_id]}
            
            # 获取官方 qrels 中的文档
            official_docs = set()
            if query_id in self.official_qrels:
                official_docs = set(self.official_qrels[query_id].keys())
            
            # 计算重叠
            overlap = demo_docs & official_docs
            
            total_demo_docs += len(demo_docs)
            total_official_docs += len(official_docs)
            total_overlaps += len(overlap)
            
            coverage_details.append({
                'query_id': query_id,
                'demo_docs': len(demo_docs),
                'official_docs': len(official_docs),
                'overlap': len(overlap),
                'coverage_rate': len(overlap) / len(demo_docs) if demo_docs else 0
            })
        
        print(f"📊 Overall Document Statistics:")
        print(f"   Total demo documents: {total_demo_docs}")
        print(f"   Total official documents: {total_official_docs}")
        print(f"   Total overlaps: {total_overlaps}")
        print(f"   Overall coverage rate: {total_overlaps/total_demo_docs*100:.1f}%")
        
        # 显示详细的覆盖情况
        print(f"\n📋 Per-Query Coverage Details:")
        print(f"{'Query ID':<10} {'Demo':<6} {'Official':<8} {'Overlap':<7} {'Coverage':<8}")
        print("-" * 50)
        
        for detail in sorted(coverage_details, key=lambda x: x['coverage_rate'], reverse=True)[:10]:
            print(f"{detail['query_id']:<10} {detail['demo_docs']:<6} {detail['official_docs']:<8} "
                  f"{detail['overlap']:<7} {detail['coverage_rate']*100:<8.1f}%")
        
        return coverage_details
    
    def analyze_relevance_distribution(self, common_queries: Set[str]):
        """分析相关性分布"""
        print(f"\n🔍 RELEVANCE DISTRIBUTION ANALYSIS")
        print("=" * 50)
        
        relevance_counts = defaultdict(int)
        query_relevance_stats = []
        
        for query_id in common_queries:
            if query_id not in self.official_qrels:
                continue
            
            query_relevance = defaultdict(int)
            for docid, rel in self.official_qrels[query_id].items():
                relevance_counts[rel] += 1
                query_relevance[rel] += 1
            
            query_relevance_stats.append({
                'query_id': query_id,
                'total_docs': len(self.official_qrels[query_id]),
                'relevant_docs': sum(1 for rel in self.official_qrels[query_id].values() if rel > 0),
                'highly_relevant': query_relevance[3],
                'relevant': query_relevance[2],
                'partially_relevant': query_relevance[1],
                'not_relevant': query_relevance[0]
            })
        
        print(f"📊 Overall Relevance Distribution:")
        total_judgments = sum(relevance_counts.values())
        for rel_level in sorted(relevance_counts.keys()):
            count = relevance_counts[rel_level]
            percentage = count / total_judgments * 100
            rel_name = {0: "Not Relevant", 1: "Partially", 2: "Relevant", 3: "Highly Relevant"}.get(rel_level, f"Level {rel_level}")
            print(f"   {rel_name}: {count} ({percentage:.1f}%)")
        
        # 显示每个查询的相关性统计
        print(f"\n📋 Per-Query Relevance Statistics:")
        print(f"{'Query ID':<10} {'Total':<6} {'Relevant':<8} {'Rate':<6}")
        print("-" * 35)
        
        for stat in sorted(query_relevance_stats, key=lambda x: x['relevant_docs'], reverse=True)[:10]:
            rate = stat['relevant_docs'] / stat['total_docs'] * 100 if stat['total_docs'] > 0 else 0
            print(f"{stat['query_id']:<10} {stat['total_docs']:<6} {stat['relevant_docs']:<8} {rate:<6.1f}%")
    
    def analyze_specific_cases(self, common_queries: Set[str]):
        """分析具体案例"""
        print(f"\n🔍 SPECIFIC CASE ANALYSIS")
        print("=" * 50)
        
        # 选择几个有代表性的查询进行详细分析
        sample_queries = list(common_queries)[:3]
        
        for query_id in sample_queries:
            print(f"\n📝 Query {query_id}:")
            
            # 显示查询文本
            query_text = self.toy_data.get(query_id, {}).get('query', 'Unknown')
            print(f"   Text: {query_text}")
            
            # 获取演示中的 top-5 文档
            demo_docs = []
            if query_id in self.demo_data:
                demo_docs = self.demo_data[query_id][:5]
            
            print(f"   Demo Top-5 Documents:")
            for i, doc in enumerate(demo_docs):
                doc_id = doc['doc_id']
                doc_text = doc['doc_text'][:80] + "..."
                
                # 检查在官方 qrels 中的相关性
                official_rel = "N/A"
                if query_id in self.official_qrels and doc_id in self.official_qrels[query_id]:
                    official_rel = self.official_qrels[query_id][doc_id]
                
                print(f"     {i+1}. [{doc_id}] Rel: {official_rel}")
                print(f"        {doc_text}")
    
    def provide_recommendations(self):
        """提供改进建议"""
        print(f"\n💡 RECOMMENDATIONS")
        print("=" * 50)
        
        print("🎯 Key Findings:")
        print("   1. Official qrels evaluation shows 0.0 scores")
        print("   2. This indicates document coverage issues, not evaluation bias")
        print("   3. Demo documents may not overlap with officially judged documents")
        
        print("\n🔧 Possible Solutions:")
        print("   1. Use TREC DL 2019 official retrieval results as baseline")
        print("   2. Download MS MARCO document collection for full evaluation")
        print("   3. Focus on queries with good document coverage")
        print("   4. Consider using TREC DL 2020/2021 data with better coverage")
        
        print("\n📚 Next Steps:")
        print("   1. Download official TREC DL 2019 baseline runs")
        print("   2. Use official top-100 results instead of toy data")
        print("   3. Implement proper document ID mapping")
        print("   4. Consider end-to-end evaluation with full MS MARCO collection")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🔍 OFFICIAL QRELS ANALYSIS")
        print("=" * 60)
        
        if not self.official_qrels or not self.demo_data:
            print("❌ Required data not available")
            return
        
        # 1. 查询覆盖分析
        common_queries = self.analyze_query_coverage()
        
        if not common_queries:
            print("❌ No common queries found")
            return
        
        # 2. 文档覆盖分析
        coverage_details = self.analyze_document_coverage(common_queries)
        
        # 3. 相关性分布分析
        self.analyze_relevance_distribution(common_queries)
        
        # 4. 具体案例分析
        self.analyze_specific_cases(common_queries)
        
        # 5. 提供建议
        self.provide_recommendations()
        
        print(f"\n✅ Analysis completed!")
        print(f"📊 Analyzed {len(common_queries)} common queries")


def main():
    """主函数"""
    analyzer = QrelsAnalyzer()
    analyzer.run_full_analysis()


if __name__ == "__main__":
    main()
