#!/usr/bin/env python3
"""
Fresh PyTerrier Demo

A clean PyTerrier demo that starts fresh without any previous initialization issues
"""

import os
import sys
import subprocess
import pandas as pd


def clean_pyterrier_environment():
    """Clean PyTerrier environment"""
    print("🧹 Cleaning PyTerrier environment...")
    
    # Remove any cached PyTerrier files
    import shutil
    
    cache_dirs = [
        os.path.expanduser("~/.pyterrier"),
        "./demo_index",
        "./test_index"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"   Removed: {cache_dir}")
            except Exception as e:
                print(f"   Could not remove {cache_dir}: {e}")


def restart_python_with_pyterrier():
    """Restart Python in a clean environment for PyTerrier"""
    print("🔄 Restarting Python with clean PyTerrier environment...")
    
    script_content = '''
import os
import pandas as pd

def run_pyterrier_demo():
    """Run PyTerrier demo in clean environment"""
    try:
        import pyterrier as pt
        
        print("🔄 Initializing PyTerrier in clean environment...")
        
        # Initialize PyTerrier
        pt.init()
        print("✅ PyTerrier initialized successfully!")
        
        # Create sample documents
        documents = [
            {"docno": "doc1", "text": "Machine learning is a subset of artificial intelligence."},
            {"docno": "doc2", "text": "Deep learning uses neural networks with multiple layers."},
            {"docno": "doc3", "text": "Information retrieval finds relevant documents from collections."},
            {"docno": "doc4", "text": "BM25 is a ranking function for search engines."},
            {"docno": "doc5", "text": "PyTerrier is a Python framework for information retrieval."}
        ]
        
        print(f"📚 Created {len(documents)} documents")
        
        # Build index
        print("🔨 Building index...")
        indexer = pt.IterDictIndexer("./clean_index", overwrite=True)
        index_ref = indexer.index(documents)
        index = pt.IndexFactory.of(index_ref)
        
        # Create retriever
        bm25 = pt.BatchRetrieve(index, wmodel="BM25")
        
        # Test search
        query_df = pd.DataFrame([{"qid": "1", "query": "machine learning"}])
        results = bm25.transform(query_df)
        
        print("\\n🔍 Search Results for 'machine learning':")
        print("=" * 50)
        
        for idx, (_, row) in enumerate(results.iterrows(), 1):
            print(f"{idx}. [{row['docno']}] Score: {row['score']:.4f}")
        
        print("\\n✅ PyTerrier demo completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ PyTerrier demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_pyterrier_demo()
    if not success:
        print("\\n💡 Try the alternative demo instead:")
        print("   python alternative_search_demo.py")
'''
    
    # Write the script
    with open("clean_pyterrier_run.py", 'w') as f:
        f.write(script_content)
    
    print("✅ Created clean PyTerrier script")
    return "clean_pyterrier_run.py"


def main():
    """Main function"""
    print("🚀 Fresh PyTerrier Demo Setup")
    print("=" * 40)
    
    # Clean environment
    clean_pyterrier_environment()
    
    # Create clean script
    script_path = restart_python_with_pyterrier()
    
    print(f"\n🎯 Running clean PyTerrier demo...")
    print("=" * 40)
    
    # Run the clean script
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, timeout=120)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n🎉 Clean PyTerrier demo completed successfully!")
        else:
            print(f"\n⚠️  Demo completed with return code: {result.returncode}")
            
    except subprocess.TimeoutExpired:
        print("❌ Demo timed out")
    except Exception as e:
        print(f"❌ Error running demo: {e}")
    
    print("\n💡 If PyTerrier still doesn't work, use the alternative:")
    print("   python alternative_search_demo.py")


if __name__ == "__main__":
    main()
