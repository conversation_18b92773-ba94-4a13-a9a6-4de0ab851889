#!/usr/bin/env python3
"""
PyTerrier DL19 BM25 演示

使用 PyTerrier 框架在 TREC Deep Learning 2019 test 数据集上演示 BM25 检索
包括索引构建、检索、评估等完整流程
"""

import os
import pandas as pd
import pyterrier as pt
from typing import Dict, List, Optional
import requests
import gzip
import json


class PyTerrierDL19Demo:
    """PyTerrier DL19 演示类"""
    
    def __init__(self, data_dir: str = "pyterrier_data"):
        """
        初始化演示
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        # 初始化 PyTerrier
        if not pt.started():
            pt.init()
            print("✅ PyTerrier initialized")
        
        self.index_path = os.path.join(data_dir, "dl19_index")
        self.index = None
        self.bm25 = None
    
    def download_dl19_data(self) -> bool:
        """下载 DL19 数据"""
        print("📥 Downloading DL19 data...")
        
        # 下载查询
        queries_url = "https://msmarco.blob.core.windows.net/msmarcoranking/msmarco-test2019-queries.tsv"
        queries_path = os.path.join(self.data_dir, "queries.tsv")
        
        # 下载 qrels
        qrels_url = "https://trec.nist.gov/data/deep/2019qrels-docs.txt"
        qrels_path = os.path.join(self.data_dir, "qrels.txt")
        
        try:
            # 下载查询文件
            if not os.path.exists(queries_path):
                print("🔄 Downloading queries...")
                response = requests.get(queries_url, timeout=60)
                if response.status_code == 200:
                    with open(queries_path, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"✅ Downloaded queries: {queries_path}")
                else:
                    print(f"⚠️  Failed to download queries (status: {response.status_code})")
                    # 创建示例查询
                    self._create_sample_queries(queries_path)
            
            # 下载 qrels 文件
            if not os.path.exists(qrels_path):
                print("🔄 Downloading qrels...")
                response = requests.get(qrels_url, timeout=60)
                if response.status_code == 200:
                    with open(qrels_path, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"✅ Downloaded qrels: {qrels_path}")
                else:
                    print(f"⚠️  Failed to download qrels (status: {response.status_code})")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ Error downloading data: {e}")
            return False
    
    def _create_sample_queries(self, queries_path: str):
        """创建示例查询（当下载失败时）"""
        print("🔧 Creating sample queries...")
        
        sample_queries = [
            "1037798\twho is robert gray",
            "104861\tcost of interior concrete flooring", 
            "1063750\twhy did the us voluntarily enter ww1",
            "1103812\twho formed the commonwealth of independent states",
            "1106007\tdefine visceral",
            "1110199\twhat is wifi vs bluetooth",
            "1112341\twhat is the daily life of thai people",
            "1113437\twhat is physical description of spruce",
            "1114646\twhat is famvir prescribed for",
            "1114819\twhat is durable medical equipment consist of"
        ]
        
        with open(queries_path, 'w', encoding='utf-8') as f:
            for query in sample_queries:
                f.write(query + '\n')
        
        print(f"✅ Created sample queries: {queries_path}")
    
    def create_sample_documents(self) -> str:
        """创建示例文档集合"""
        docs_path = os.path.join(self.data_dir, "documents.jsonl")
        
        if os.path.exists(docs_path):
            print(f"✅ Documents already exist: {docs_path}")
            return docs_path
        
        print("🔧 Creating sample document collection...")
        
        # 从我们的 toy data 创建文档集合
        toy_data_path = "toy_data/dl19_bm25_top20.jsonl"
        
        if not os.path.exists(toy_data_path):
            print("❌ Toy data not found, creating minimal document set...")
            self._create_minimal_documents(docs_path)
            return docs_path
        
        # 从 toy data 提取文档
        documents = {}
        
        with open(toy_data_path, 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line)
                for hit in item['hits']:
                    doc_id = hit['docid']
                    content = hit['content']
                    documents[doc_id] = content
        
        # 保存为 JSONL 格式
        with open(docs_path, 'w', encoding='utf-8') as f:
            for doc_id, content in documents.items():
                doc = {
                    'docno': doc_id,
                    'text': content
                }
                f.write(json.dumps(doc) + '\n')
        
        print(f"✅ Created document collection: {docs_path}")
        print(f"📊 Total documents: {len(documents)}")
        
        return docs_path
    
    def _create_minimal_documents(self, docs_path: str):
        """创建最小文档集合"""
        minimal_docs = [
            {
                'docno': 'doc1',
                'text': 'Robert Gray was an American merchant sea captain who is known for his achievements in connection with two trading voyages to the northern Pacific coast of North America.'
            },
            {
                'docno': 'doc2', 
                'text': 'The cost of interior concrete flooring varies depending on the complexity of the job and your location. Basic concrete floors can cost $2-6 per square foot.'
            },
            {
                'docno': 'doc3',
                'text': 'The United States entered World War I in 1917 due to several factors including unrestricted submarine warfare by Germany and the Zimmermann Telegram.'
            },
            {
                'docno': 'doc4',
                'text': 'The Commonwealth of Independent States was formed by the leaders of Russia, Ukraine, and Belarus following the dissolution of the Soviet Union.'
            },
            {
                'docno': 'doc5',
                'text': 'Visceral refers to something felt in or as if in the internal organs of the body, relating to deep inward feelings rather than intellect.'
            }
        ]
        
        with open(docs_path, 'w', encoding='utf-8') as f:
            for doc in minimal_docs:
                f.write(json.dumps(doc) + '\n')
        
        print(f"✅ Created minimal document collection: {docs_path}")
    
    def build_index(self, docs_path: str) -> bool:
        """构建索引"""
        
        if os.path.exists(self.index_path) and os.listdir(self.index_path):
            print(f"✅ Index already exists: {self.index_path}")
            self.index = pt.IndexFactory.of(self.index_path)
            return True
        
        print("🔨 Building PyTerrier index...")
        
        try:
            # 读取文档
            print("📖 Reading documents...")
            
            # 使用 PyTerrier 的文档迭代器
            def doc_iterator():
                with open(docs_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        doc = json.loads(line.strip())
                        yield {
                            'docno': doc['docno'],
                            'text': doc['text']
                        }
            
            # 构建索引
            indexer = pt.IterDictIndexer(self.index_path, verbose=True)
            index_ref = indexer.index(doc_iterator())
            
            self.index = pt.IndexFactory.of(index_ref)
            
            print(f"✅ Index built successfully!")
            print(f"📊 Index statistics:")
            print(f"   Documents: {self.index.getCollectionStatistics().getNumberOfDocuments()}")
            print(f"   Terms: {self.index.getCollectionStatistics().getNumberOfUniqueTerms()}")
            print(f"   Tokens: {self.index.getCollectionStatistics().getNumberOfTokens()}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error building index: {e}")
            return False
    
    def setup_bm25(self) -> bool:
        """设置 BM25 检索器"""
        
        if self.index is None:
            print("❌ Index not available")
            return False
        
        try:
            # 创建 BM25 检索器
            self.bm25 = pt.BatchRetrieve(self.index, wmodel="BM25")
            
            print("✅ BM25 retriever initialized")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up BM25: {e}")
            return False
    
    def load_queries(self) -> pd.DataFrame:
        """加载查询"""
        queries_path = os.path.join(self.data_dir, "queries.tsv")
        
        try:
            # 读取查询文件
            queries = []
            with open(queries_path, 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        qid, query = parts[0], parts[1]
                        queries.append({'qid': qid, 'query': query})
            
            queries_df = pd.DataFrame(queries)
            print(f"✅ Loaded {len(queries_df)} queries")
            
            return queries_df
            
        except Exception as e:
            print(f"❌ Error loading queries: {e}")
            return pd.DataFrame()
    
    def load_qrels(self) -> pd.DataFrame:
        """加载相关性判断"""
        qrels_path = os.path.join(self.data_dir, "qrels.txt")
        
        if not os.path.exists(qrels_path):
            print("⚠️  Qrels file not found")
            return pd.DataFrame()
        
        try:
            # 读取 qrels 文件
            qrels = []
            with open(qrels_path, 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 4:
                        qid, _, docno, label = parts[0], parts[1], parts[2], int(parts[3])
                        qrels.append({
                            'qid': qid,
                            'docno': docno, 
                            'label': label
                        })
            
            qrels_df = pd.DataFrame(qrels)
            print(f"✅ Loaded {len(qrels_df)} qrels")
            
            return qrels_df
            
        except Exception as e:
            print(f"❌ Error loading qrels: {e}")
            return pd.DataFrame()
    
    def run_retrieval(self, queries_df: pd.DataFrame, top_k: int = 20) -> pd.DataFrame:
        """运行检索"""
        
        if self.bm25 is None:
            print("❌ BM25 retriever not available")
            return pd.DataFrame()
        
        print(f"🔍 Running BM25 retrieval for {len(queries_df)} queries...")
        
        try:
            # 执行检索
            results = self.bm25.transform(queries_df)
            
            # 限制结果数量
            if top_k > 0:
                results = results.groupby('qid').head(top_k).reset_index(drop=True)
            
            print(f"✅ Retrieved {len(results)} results")
            print(f"📊 Average results per query: {len(results) / len(queries_df):.1f}")
            
            return results
            
        except Exception as e:
            print(f"❌ Error during retrieval: {e}")
            return pd.DataFrame()
    
    def evaluate_results(self, results_df: pd.DataFrame, qrels_df: pd.DataFrame) -> Dict:
        """评估检索结果"""
        
        if results_df.empty or qrels_df.empty:
            print("❌ Cannot evaluate: missing results or qrels")
            return {}
        
        print("📊 Evaluating retrieval results...")
        
        try:
            # 使用 PyTerrier 的评估功能
            evaluator = pt.Evaluate(
                results_df, 
                qrels_df, 
                metrics=['map', 'ndcg_cut_10', 'ndcg_cut_20', 'P_10', 'P_20', 'recall_10', 'recall_20']
            )
            
            print("✅ Evaluation completed")
            
            # 打印结果
            print("\n📈 Evaluation Results:")
            print("=" * 50)
            for metric, value in evaluator.items():
                print(f"{metric:<15}: {value:.4f}")
            
            return evaluator
            
        except Exception as e:
            print(f"❌ Error during evaluation: {e}")
            return {}
    
    def show_sample_results(self, results_df: pd.DataFrame, queries_df: pd.DataFrame, num_queries: int = 3):
        """显示示例检索结果"""
        
        if results_df.empty or queries_df.empty:
            print("❌ No results to show")
            return
        
        print(f"\n🔍 Sample Retrieval Results (Top {num_queries} queries):")
        print("=" * 80)
        
        # 获取查询文本映射
        query_map = dict(zip(queries_df['qid'], queries_df['query']))
        
        # 显示前几个查询的结果
        unique_qids = results_df['qid'].unique()[:num_queries]
        
        for qid in unique_qids:
            query_text = query_map.get(qid, "Unknown")
            query_results = results_df[results_df['qid'] == qid].head(5)
            
            print(f"\n📝 Query {qid}: {query_text}")
            print("-" * 60)
            
            for idx, (_, row) in enumerate(query_results.iterrows(), 1):
                score = row.get('score', 0)
                docno = row.get('docno', 'Unknown')
                print(f"  {idx}. [{docno}] Score: {score:.4f}")
    
    def run_complete_demo(self):
        """运行完整演示"""
        
        print("🚀 PyTerrier DL19 BM25 Demo")
        print("=" * 50)
        
        # 1. 下载数据
        if not self.download_dl19_data():
            print("⚠️  Using sample data due to download issues")
        
        # 2. 创建文档集合
        docs_path = self.create_sample_documents()
        
        # 3. 构建索引
        if not self.build_index(docs_path):
            print("❌ Failed to build index")
            return
        
        # 4. 设置 BM25
        if not self.setup_bm25():
            print("❌ Failed to setup BM25")
            return
        
        # 5. 加载查询和 qrels
        queries_df = self.load_queries()
        qrels_df = self.load_qrels()
        
        if queries_df.empty:
            print("❌ No queries available")
            return
        
        # 6. 运行检索
        results_df = self.run_retrieval(queries_df, top_k=20)
        
        if results_df.empty:
            print("❌ No retrieval results")
            return
        
        # 7. 显示示例结果
        self.show_sample_results(results_df, queries_df)
        
        # 8. 评估结果（如果有 qrels）
        if not qrels_df.empty:
            evaluation = self.evaluate_results(results_df, qrels_df)
        else:
            print("⚠️  No qrels available for evaluation")
        
        # 9. 保存结果
        results_path = os.path.join(self.data_dir, "bm25_results.csv")
        results_df.to_csv(results_path, index=False)
        print(f"\n💾 Results saved to: {results_path}")
        
        print("\n✅ Demo completed successfully!")
        print("\n📚 PyTerrier Features Demonstrated:")
        print("   • Document indexing with IterDictIndexer")
        print("   • BM25 retrieval with BatchRetrieve")
        print("   • Automatic evaluation with built-in metrics")
        print("   • Pandas DataFrame integration")
        print("   • Flexible query and document handling")


def main():
    """主函数"""
    
    # 检查 PyTerrier 依赖
    try:
        import pyterrier as pt
    except ImportError:
        print("❌ PyTerrier not installed. Please install with:")
        print("   pip install python-terrier")
        return
    
    # 运行演示
    demo = PyTerrierDL19Demo()
    demo.run_complete_demo()


if __name__ == "__main__":
    main()
