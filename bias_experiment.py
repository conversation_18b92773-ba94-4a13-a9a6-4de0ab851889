#!/usr/bin/env python3
"""
评估偏差实验

这个脚本验证我们关于评估偏差的假设：
1. 反向实验：用 LLM 排序创建 qrels，评估 BM25
2. 随机基线：比较随机排序的表现
3. 人工评估：简单的人工评估示例
"""

import json
import random
import numpy as np
from typing import Dict, List, Tuple
from src.llm4ranking.evaluation.metrics import RankingEvaluator


class BiasExperiment:
    """评估偏差实验类"""
    
    def __init__(self):
        self.evaluator = RankingEvaluator()
        self.load_demo_results()
    
    def load_demo_results(self):
        """加载演示结果"""
        try:
            with open("results/bm25_results.json", 'r', encoding='utf-8') as f:
                self.bm25_results = json.load(f)
            
            with open("results/reranked_results.json", 'r', encoding='utf-8') as f:
                self.llm_results = json.load(f)
            
            print(f"✅ Loaded results for {len(self.bm25_results)} queries")
            
        except FileNotFoundError:
            print("❌ Results files not found. Please run the demo first.")
            self.bm25_results = {}
            self.llm_results = {}
    
    def create_bm25_based_qrels(self) -> Dict[str, Dict[str, int]]:
        """创建基于 BM25 的 qrels（当前方法）"""
        qrels = {}
        
        for query_id, docs in self.bm25_results.items():
            qrels[query_id] = {}
            
            for i, doc in enumerate(docs):
                doc_id = doc['doc_id']
                if i < 5:
                    qrels[query_id][doc_id] = 3
                elif i < 10:
                    qrels[query_id][doc_id] = 2
                elif i < 15:
                    qrels[query_id][doc_id] = 1
                else:
                    qrels[query_id][doc_id] = 0
        
        return qrels
    
    def create_llm_based_qrels(self) -> Dict[str, Dict[str, int]]:
        """创建基于 LLM 重排的 qrels（反向实验）"""
        qrels = {}
        
        for query_id, docs in self.llm_results.items():
            qrels[query_id] = {}
            
            for i, doc in enumerate(docs):
                doc_id = doc['doc_id']
                if i < 5:
                    qrels[query_id][doc_id] = 3
                elif i < 10:
                    qrels[query_id][doc_id] = 2
                elif i < 15:
                    qrels[query_id][doc_id] = 1
                else:
                    qrels[query_id][doc_id] = 0
        
        return qrels
    
    def create_random_qrels(self) -> Dict[str, Dict[str, int]]:
        """创建随机的 qrels（控制实验）"""
        qrels = {}
        
        for query_id, docs in self.bm25_results.items():
            qrels[query_id] = {}
            
            # 随机分配相关性分数
            doc_ids = [doc['doc_id'] for doc in docs]
            random.shuffle(doc_ids)
            
            for i, doc_id in enumerate(doc_ids):
                if i < 5:
                    qrels[query_id][doc_id] = 3
                elif i < 10:
                    qrels[query_id][doc_id] = 2
                elif i < 15:
                    qrels[query_id][doc_id] = 1
                else:
                    qrels[query_id][doc_id] = 0
        
        return qrels
    
    def create_random_ranking(self) -> Dict[str, List[str]]:
        """创建随机排序（作为基线）"""
        random_results = {}
        
        for query_id, docs in self.bm25_results.items():
            doc_ids = [doc['doc_id'] for doc in docs]
            random.shuffle(doc_ids)
            random_results[query_id] = doc_ids
        
        return random_results
    
    def convert_to_ranking(self, results: Dict) -> Dict[str, List[str]]:
        """将结果转换为排序格式"""
        rankings = {}
        for query_id, docs in results.items():
            rankings[query_id] = [doc['doc_id'] for doc in docs]
        return rankings
    
    def run_bias_experiment(self):
        """运行完整的偏差实验"""
        if not self.bm25_results or not self.llm_results:
            print("❌ No results to analyze")
            return
        
        print("🧪 Running Bias Experiment")
        print("=" * 60)
        
        # 准备数据
        bm25_rankings = self.convert_to_ranking(self.bm25_results)
        llm_rankings = self.convert_to_ranking(self.llm_results)
        random_rankings = self.create_random_ranking()
        
        # 创建不同的 qrels
        bm25_qrels = self.create_bm25_based_qrels()
        llm_qrels = self.create_llm_based_qrels()
        random_qrels = self.create_random_qrels()
        
        print("\n📊 Experiment Results:")
        print("-" * 60)
        
        # 实验 1: 当前评估（BM25 qrels）
        print("\n🔬 Experiment 1: BM25-based qrels (Current)")
        bm25_vs_bm25 = self.evaluator.evaluate_run(bm25_rankings, bm25_qrels, [1, 5, 10])
        llm_vs_bm25 = self.evaluator.evaluate_run(llm_rankings, bm25_qrels, [1, 5, 10])
        random_vs_bm25 = self.evaluator.evaluate_run(random_rankings, bm25_qrels, [1, 5, 10])
        
        self.print_comparison("BM25 qrels", {
            "BM25": bm25_vs_bm25,
            "LLM": llm_vs_bm25,
            "Random": random_vs_bm25
        })
        
        # 实验 2: 反向评估（LLM qrels）
        print("\n🔬 Experiment 2: LLM-based qrels (Reverse)")
        bm25_vs_llm = self.evaluator.evaluate_run(bm25_rankings, llm_qrels, [1, 5, 10])
        llm_vs_llm = self.evaluator.evaluate_run(llm_rankings, llm_qrels, [1, 5, 10])
        random_vs_llm = self.evaluator.evaluate_run(random_rankings, llm_qrels, [1, 5, 10])
        
        self.print_comparison("LLM qrels", {
            "BM25": bm25_vs_llm,
            "LLM": llm_vs_llm,
            "Random": random_vs_llm
        })
        
        # 实验 3: 随机评估（Random qrels）
        print("\n🔬 Experiment 3: Random qrels (Control)")
        bm25_vs_random = self.evaluator.evaluate_run(bm25_rankings, random_qrels, [1, 5, 10])
        llm_vs_random = self.evaluator.evaluate_run(llm_rankings, random_qrels, [1, 5, 10])
        random_vs_random = self.evaluator.evaluate_run(random_rankings, random_qrels, [1, 5, 10])
        
        self.print_comparison("Random qrels", {
            "BM25": bm25_vs_random,
            "LLM": llm_vs_random,
            "Random": random_vs_random
        })
        
        # 分析结果
        self.analyze_bias_results()
    
    def print_comparison(self, qrels_type: str, results: Dict[str, Dict]):
        """打印比较结果"""
        print(f"\nUsing {qrels_type}:")
        print(f"{'Method':<10} {'NDCG@1':<8} {'NDCG@5':<8} {'NDCG@10':<8}")
        print("-" * 40)
        
        for method, metrics in results.items():
            ndcg1 = metrics.get('NDCG@1', 0)
            ndcg5 = metrics.get('NDCG@5', 0)
            ndcg10 = metrics.get('NDCG@10', 0)
            print(f"{method:<10} {ndcg1:<8.3f} {ndcg5:<8.3f} {ndcg10:<8.3f}")
    
    def analyze_bias_results(self):
        """分析偏差实验结果"""
        print("\n" + "=" * 60)
        print("🎯 BIAS ANALYSIS")
        print("=" * 60)
        
        print("\n📋 Key Observations:")
        print("1. 在 BM25 qrels 下，BM25 表现最好（预期）")
        print("2. 在 LLM qrels 下，LLM 应该表现最好")
        print("3. 随机排序在所有情况下都应该表现最差")
        print("4. 如果 LLM 在 LLM qrels 下表现更好，说明存在评估偏差")
        
        print("\n💡 Implications:")
        print("- 评估结果很大程度上取决于 qrels 的创建方式")
        print("- 使用 BM25 排序创建的 qrels 天然偏向 BM25")
        print("- 真实的性能评估需要独立的人工标注数据")
        print("- LLM 重排可能在语义理解上更准确，但在当前评估下被'惩罚'")
        
        print("\n🔧 Recommendations:")
        print("1. 使用官方 TREC DL 2019 qrels 进行公平评估")
        print("2. 进行用户研究或 A/B 测试")
        print("3. 考虑多种评估指标和方法")
        print("4. 分析具体案例，理解重排的语义合理性")
    
    def simple_human_evaluation(self):
        """简单的人工评估示例"""
        print("\n" + "=" * 60)
        print("👥 SIMPLE HUMAN EVALUATION")
        print("=" * 60)
        
        if not self.bm25_results or not self.llm_results:
            print("❌ No results to evaluate")
            return
        
        # 选择一个查询进行人工评估
        query_id = list(self.bm25_results.keys())[0]
        bm25_docs = self.bm25_results[query_id][:3]
        llm_docs = self.llm_results[query_id][:3]
        
        # 加载查询文本
        with open("toy_data/dl19_bm25_top20.jsonl", 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line)
                if str(item['hits'][0]['qid']) == query_id:
                    query_text = item['query']
                    break
        
        print(f"\n🔍 Query: {query_text}")
        print(f"📝 Query ID: {query_id}")
        
        print(f"\n📊 BM25 Top-3:")
        for i, doc in enumerate(bm25_docs):
            print(f"  {i+1}. {doc['doc_text'][:80]}...")
        
        print(f"\n🤖 LLM Top-3:")
        for i, doc in enumerate(llm_docs):
            print(f"  {i+1}. {doc['doc_text'][:80]}...")
        
        print(f"\n❓ Human Evaluation Questions:")
        print("1. 哪个排序更好地回答了查询？")
        print("2. LLM 重排是否提供了更相关的文档？")
        print("3. 是否有明显的语义理解改进？")
        
        print(f"\n💭 Analysis Hints:")
        print("- 比较文档与查询的语义匹配度")
        print("- 考虑文档的信息完整性")
        print("- 评估答案的直接性和准确性")


def main():
    """主函数"""
    experiment = BiasExperiment()
    
    print("🎯 Evaluation Bias Experiment")
    print("This experiment demonstrates why LLM reranking appears to perform worse")
    print("when evaluated using BM25-based relevance judgments.")
    print()
    
    # 运行偏差实验
    experiment.run_bias_experiment()
    
    # 简单人工评估
    experiment.simple_human_evaluation()
    
    print("\n" + "=" * 60)
    print("✅ Experiment completed!")
    print("📖 See performance_analysis.md for detailed explanation")
    print("=" * 60)


if __name__ == "__main__":
    main()
