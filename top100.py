import pyterrier as pt
import json
import pandas as pd
import os
from typing import Dict, List, Any
from tqdm import tqdm

class DL19BM25RetrieverWithText:
    def __init__(self, index_path: str):
        """
        Initialize the retriever with existing index
        
        Args:
            index_path: Path to the existing PyTerrier index
        """
        # Initialize PyTerrier if not already done
        if not pt.started():
            pt.init()
            
        self.index_path = index_path
        self.index = None
        self.topics = None
        self.qrels = None
        
    def load_index(self):
        """Load the existing PyTerrier index"""
        print(f"Loading index from {self.index_path}...")
        self.index = pt.IndexFactory.of(self.index_path)
        print(f"Index loaded. Number of documents: {self.index.getCollectionStatistics().getNumberOfDocuments()}")
        return self.index
    
    def load_dl19_dataset(self):
        """Load DL19 queries and qrels"""
        print("Loading DL19 dataset...")
        dataset = pt.get_dataset("irds:msmarco-passage/trec-dl-2019/judged")
        
        self.topics = dataset.get_topics()
        self.qrels = dataset.get_qrels()
        
        print(f"Loaded {len(self.topics)} queries")
        print(f"Loaded {len(self.qrels)} relevance judgments")
        
        return self.topics, self.qrels
    
    def create_bm25_retriever_with_text(self, k1: float = 1.2, b: float = 0.75, num_results: int = 100):
        """
        Create BM25 retriever that also retrieves document text
        
        Args:
            k1: BM25 k1 parameter
            b: BM25 b parameter
            num_results: Number of results to retrieve per query
            
        Returns:
            BM25 retriever pipeline with text
        """
        # Create BM25 retriever
        bm25 = pt.BatchRetrieve(
            self.index,
            wmodel="BM25",
            controls={
                "c": b,
                "k1": k1,
            },
            verbose=True,
            num_results=num_results
        )
        
        # Add text retrieval pipeline
        # This will fetch the document text from the index metadata
        text_pipeline = bm25 >> pt.text.get_text(self.index, "text")
        
        return text_pipeline
    
    def retrieve_with_text(self, retriever_pipeline) -> pd.DataFrame:
        """
        Run retrieval and get results with document text
        
        Args:
            retriever_pipeline: The retriever pipeline with text retrieval
            
        Returns:
            DataFrame with retrieval results including text
        """
        print("Running BM25 retrieval with text for all queries...")
        
        # Run retrieval
        results = retriever_pipeline.transform(self.topics)
        
        print(f"Retrieved {len(results)} total results with text")
        return results
    
    def format_results_with_text(self, results_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Format retrieval results with text for JSON output
        
        Args:
            results_df: DataFrame with retrieval results
            
        Returns:
            Dictionary formatted for JSON
        """
        print("Formatting results with text...")
        
        # Initialize output dictionary
        output_data = {
            "retrieval_method": "BM25",
            "parameters": {
                "k1": 1.2,
                "b": 0.75,
                "num_results": 100
            },
            "queries": {}
        }
        
        # Group results by query
        grouped = results_df.groupby('qid')
        
        for qid, group in tqdm(grouped, desc="Processing queries"):
            # Get query text
            query_text = self.topics[self.topics['qid'] == qid]['query'].iloc[0]
            
            # Sort by rank
            group = group.sort_values('rank')
            
            # Format documents for this query
            documents = []
            for _, row in group.iterrows():
                doc = {
                    "docno": row['docno'],
                    "rank": int(row['rank']),
                    "score": float(row['score']),
                    "text": row.get('text', '')  # Document text
                }
                documents.append(doc)
            
            # Add query information
            output_data["queries"][qid] = {
                "query_text": query_text,
                "num_results": len(documents),
                "documents": documents
            }
        
        # Add metadata
        output_data["metadata"] = {
            "total_queries": len(output_data["queries"]),
            "index_path": self.index_path,
            "dataset": "TREC DL 2019",
            "includes_text": True
        }
        
        return output_data
    
    def save_results_to_json(self, results_data: Dict[str, Any], output_file: str):
        """
        Save results to JSON file
        
        Args:
            results_data: The formatted results dictionary
            output_file: Output JSON file path
        """
        print(f"Saving results to {output_file}...")
        
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Save to JSON with indentation for readability
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        # Calculate file size
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # Convert to MB
        print(f"Results saved successfully! File size: {file_size:.2f} MB")
        
        # Print summary statistics
        print("\nSummary:")
        print(f"- Total queries: {results_data['metadata']['total_queries']}")
        print(f"- Documents per query: {results_data['parameters']['num_results']}")
        total_docs = sum(len(q['documents']) for q in results_data['queries'].values())
        print(f"- Total documents retrieved: {total_docs}")
        
        # Check text availability
        sample_query = list(results_data['queries'].values())[0]
        sample_doc = sample_query['documents'][0]
        has_text = bool(sample_doc.get('text', '').strip())
        print(f"- Document text included: {'Yes' if has_text else 'No'}")
        
        if has_text:
            avg_text_length = sum(
                len(doc['text']) 
                for q in results_data['queries'].values() 
                for doc in q['documents']
            ) / total_docs
            print(f"- Average text length: {avg_text_length:.0f} characters")
    
    def verify_results_with_text(self, json_file: str):
        """
        Verify the saved JSON results including text content
        """
        print(f"\nVerifying results from {json_file}...")
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ Loaded {len(data['queries'])} queries")
        
        # Check a sample query
        sample_qid = list(data['queries'].keys())[0]
        sample_query = data['queries'][sample_qid]
        print(f"\nSample query {sample_qid}:")
        print(f"  Query text: {sample_query['query_text']}")
        print(f"  Number of results: {sample_query['num_results']}")
        print(f"  Top 3 documents:")
        
        for i, doc in enumerate(sample_query['documents'][:3]):
            text_preview = doc['text'][:100] + "..." if len(doc['text']) > 100 else doc['text']
            print(f"    {i+1}. {doc['docno']} (score: {doc['score']:.4f})")
            print(f"       Text: {text_preview}")

def main():
    # Configuration
    INDEX_PATH = "./msmarco-passage/index"  # Update this to your actual index path
    OUTPUT_DIR = "./retrieval_results"
    
    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Initialize retriever
    retriever = DL19BM25RetrieverWithText(INDEX_PATH)
    
    # Load index
    retriever.load_index()
    
    # Load DL19 dataset
    retriever.load_dl19_dataset()
    
    # Create BM25 retriever with text retrieval
    retriever_pipeline = retriever.create_bm25_retriever_with_text(
        k1=1.2, 
        b=0.75, 
        num_results=100
    )
    
    # Retrieve results with text
    results_df = retriever.retrieve_with_text(retriever_pipeline)
    
    # Format results
    results_data = retriever.format_results_with_text(results_df)
    
    # Save to JSON
    output_file = os.path.join(OUTPUT_DIR, "dl19_bm25_top100_with_text.json")
    retriever.save_results_to_json(results_data, output_file)
    
    # Verify results
    retriever.verify_results_with_text(output_file)
    
    print("\n✓ All tasks completed successfully!")

if __name__ == "__main__":
    import sys
    
    # Get index path from command line or use default
    if len(sys.argv) > 1:
        index_path = sys.argv[1]
    else:
        index_path = "./msmarco-passage/index"
    
    print(f"Using index path: {index_path}")
    
    # Initialize PyTerrier
    if not pt.started():
        pt.init()
    
    # Simple direct approach
    print("\nLoading index and dataset...")
    index = pt.IndexFactory.of(index_path)
    
    # Load DL19
    dataset = pt.get_dataset("irds:msmarco-passage/trec-dl-2019/judged")
    topics = dataset.get_topics()
    
    # Create BM25 retriever
    bm25 = pt.BatchRetrieve(
        index,
        wmodel="BM25",
        controls={"c": 0.75, "k1": 1.2},
        num_results=100
    )
    
    # Add text retrieval
    pipeline = bm25 >> pt.text.get_text(index, "text")
    
    # Run retrieval
    print("Running retrieval with text...")
    results = pipeline.transform(topics)
    
    # Prepare output
    output = {
        "method": "BM25",
        "parameters": {"k1": 1.2, "b": 0.75, "num_results": 100},
        "queries": {}
    }
    
    # Process results
    print("Processing results...")
    for qid, group in tqdm(results.groupby('qid')):
        query_text = topics[topics['qid'] == qid]['query'].iloc[0]
        docs = []
        
        for _, row in group.sort_values('rank').iterrows():
            docs.append({
                "docno": row['docno'],
                "rank": int(row['rank']),
                "score": float(row['score']),
                "text": row['text']  # Include document text
            })
        
        output["queries"][qid] = {
            "query_text": query_text,
            "documents": docs
        }
    
    # Save with text
    output_file = "dl19_bm25_top100_with_text.json"
    print(f"\nSaving to {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output, f, indent=2, ensure_ascii=False)
    
    # Report file size
    file_size = os.path.getsize(output_file) / (1024 * 1024)
    print(f"✓ Saved successfully! File size: {file_size:.2f} MB")
    print(f"✓ Total queries: {len(output['queries'])}")
